<div class="list-work-container d-flex align-items-center justify-content-between">
	<div class="d-flex">
    <button
      *ngIf="isSuperAdmin" 
      type="button" class="btn btn-icon btn-outline-secondary mr-1"
      ngbTooltip="Danh sách người dùng CMS"
      (click)="modalOpen(CmsUserListModal, null, 'lg')"
      rippleEffect>
      <span [data-feather]="'users'"></span>
    </button>

		<button 
			type="button" class="btn btn-icon btn-outline-secondary mr-1"
			ngbTooltip="Thống kê"
			(click)="modalOpen(CmsDashboardModal, null, 'sm')"
			rippleEffect>
			<span [data-feather]="'bar-chart'"></span>
		</button>
		
		<ng-select
			[items]="[
				{ label: 'Soạn thảo văn bản', value: 'content' },
				{ label: 'Câu hỏi', value: 'qa' }
			]"
			bindLabel="label"
			bindValue="value"
			[multiple]="true"
			[closeOnSelect]="false"
			placeholder="Loại công việc"
			[(ngModel)]="selectedWorkTypes">
		</ng-select>
	</div>

	<!-- UI hiển thị khi có thẻ được chọn -->
  <ngb-alert *ngIf="selectedWork.length > 0" class="mb-0" [type]="'info'" [dismissible]="false">
    <div class="alert-body py-0 px-1">
      <strong class="text-dark mr-25 h-100">Đã chọn {{ selectedWork.length }} thẻ</strong>
      <button 
        type="button" 
        class="btn btn-sm btn-flat-primary mr-25"
        (click)="openAssignWriterModal()"
        ngbTooltip="Gán người viết cho các thẻ đã chọn">
        <span data-feather="pen-tool" class="me-1"></span>
        Gán người viết
      </button>
      <button 
        type="button" 
        class="btn btn-sm btn-flat-success mr-25"
        (click)="openAssignReviewerModal()"
        ngbTooltip="Gán người phê duyệt cho các thẻ đã chọn">
        <span data-feather="check-circle" class="me-1"></span>
        Gán người phê duyệt
      </button>
      <button 
        type="button" 
        class="btn btn-sm btn-icon btn-flat-dark"
        (click)="clearWorkSelection()"
        ngbTooltip="Bỏ chọn tất cả">
        <span data-feather="x" class="me-1"></span>
      </button>
    </div>
  </ngb-alert>

  <button class="btn btn-primary ms-2" (click)="modalOpen(modalContent)">
    <span data-feather="plus" class="me-50"></span>
    <span>Thêm công việc</span>
  </button>
</div>

<div class="overflow-auto white-space-nowrap scroll-bar-width-thin">
  <div class="d-flex flex-nowrap">
    <div class="col-md-2 col-12 col-lg pl-0 min-width-260px max-width-320px kanban-col"
      [class.drop-allowed]="isColAllowed('multiple-list-group-draft')"
      [class.drop-blocked]="isDragging && !isColAllowed('multiple-list-group-draft')">
      <ngb-alert [type]="'dark'" [dismissible]="false">
        <div class="alert-body text-center my-2">
          <strong>
            Mới khởi tạo ({{ listDraft?.length || 0 }})
          </strong>
        </div>
      </ngb-alert>
      <div class="kanban-intent" *ngIf="isDragging && isIntentAllowed('DRAFT')">
        <span class="kanban-chip"
              [class.active]="hoverColId === 'multiple-list-group-draft'">
          {{ statusLabel('DRAFT') }}
        </span>
      </div>
      <ul dragula="multiple-list-group" [dragulaModel]="listDraft" class="draggable list-group min-height-100px max-height-70vh overflow-y-auto" id="multiple-list-group-draft">
        <li *ngFor="let item of listDraft" 
            class="list-group-item custom-item-drag-drop mb-1 mt-1px" 
            [class.selected-work]="isWorkSelected(item)"
            (click)="onWorkItemClick($event, item)">
					<div class="card p-0 m-0 bg-transparent shadow-none">
						<div class="card-header p-0">
							<div *ngIf="item.class=='content'" class="badge badge-primary">
								<i data-feather="pen-tool" class="mr-25"></i>
								<span>Soạn thảo văn bản</span>
							</div>
							<div *ngIf="item.class=='qa'" class="badge badge-primary">
								<i data-feather="search" class="mr-25"></i>
								<span>Câu hỏi</span>
							</div>
						</div>
						
						<div class="card-body px-0 pb-0 pt-1">
							<h5>{{ item.title || 'Không có tiêu đề' }}</h5>

							<!-- Hiển thị lĩnh vực cho QA -->
							<div *ngIf="item.class === 'qa' && (item.field || item.fields)" class="mb-1">
								<small class="text-info">
									<i data-feather="tag" class="mr-25"></i>
									{{ getFieldName(item.field || item.fields) }}
								</small>
							</div>

							<div class="mt-1">
                <div>
                  <small class="text-muted d-block">Khởi tạo: {{ formatRelativeTime(item.created_at) }}</small>
                  <small class="text-muted d-block">Cập nhật: {{ formatRelativeTime(item.updated_at) }}</small>
                </div>
							</div>

              <div class="avatar-group d-flex justify-content-end">
                <ng-container *ngFor="let user of [item.writer, item.reviewer]">
                  <div
                    *ngIf="user"
                    data-toggle="tooltip"
                    data-popup="tooltip-custom"
                    data-placement="bottom"
                    placement="top"
                    container="body"
                    [ngbTooltip]="user.fullname"
                    class="avatar pull-up"
                  >
                    <img
                      [src]="user.avatar || 'assets/images/portrait/small/users.png'"
                      alt="Avatar"
                      width="24"
                      height="24"
                    />
                  </div>
                </ng-container>
              </div>
            </div>
					</div>
        </li>
      </ul>
    </div>
    <div class="col-md-2 col-12 col-lg min-width-260px max-width-320px kanban-col"
         [class.drop-allowed]="isColAllowed('multiple-list-group-reviewing')"
         [class.drop-blocked]="isDragging && !isColAllowed('multiple-list-group-writing')">
      <ngb-alert [type]="'primary'" [dismissible]="false">
        <div class="alert-body text-center my-2">
          <strong>
            Đang viết ({{ listWriting?.length || 0 }})
          </strong>
        </div>
      </ngb-alert>
      <div class="kanban-intent" *ngIf="isDragging && isIntentAllowed('WRITING')">
        <span class="kanban-chip"
              [class.active]="hoverColId === 'multiple-list-group-writing'">
          {{ statusLabel('WRITING') }}
        </span>
      </div>
      <ul dragula="multiple-list-group" [dragulaModel]="listWriting" class="draggable list-group min-height-100px max-height-70vh overflow-y-auto" id="multiple-list-group-writing">
        <li *ngFor="let item of listWriting" 
            class="list-group-item custom-item-drag-drop mb-1 mt-1px" 
            [class.selected-work]="isWorkSelected(item)"
            (click)="onWorkItemClick($event, item)">
					<div class="card p-0 m-0 bg-transparent shadow-none">
						<div class="card-header p-0">
							<div *ngIf="item.class=='content'" class="badge badge-primary">
								<i data-feather="pen-tool" class="mr-25"></i>
								<span>Soạn thảo văn bản</span>
							</div>
							<div *ngIf="item.class=='qa'" class="badge badge-primary">
								<i data-feather="search" class="mr-25"></i>
								<span>Câu hỏi</span>
							</div>
						</div>
						
						<div class="card-body px-0 pb-0 pt-1">
							<h5>{{ item.title || 'Không có tiêu đề' }}</h5>

							<!-- Hiển thị lĩnh vực cho QA -->
							<div *ngIf="item.class === 'qa' && (item.field || item.fields)" class="mb-1">
								<small class="text-info">
									<i data-feather="tag" class="mr-25"></i>
									{{ getFieldName(item.field || item.fields) }}
								</small>
							</div>

							<div class="mt-1">
								<small class="text-muted d-block">Khởi tạo: {{ formatRelativeTime(item.created_at) }}</small>
								<small class="text-muted d-block">Cập nhật: {{ formatRelativeTime(item.updated_at) }}</small>
							</div>

              
              <div class="avatar-group d-flex justify-content-end">
                <ng-container *ngFor="let user of [item.writer, item.reviewer]">
                  <div
                    *ngIf="user"
                    data-toggle="tooltip"
                    data-popup="tooltip-custom"
                    data-placement="bottom"
                    placement="top"
                    container="body"
                    [ngbTooltip]="user.fullname"
                    class="avatar pull-up"
                  >
                    <img
                      [src]="user.avatar || 'assets/images/portrait/small/users.png'"
                      alt="Avatar"
                      width="24"
                      height="24"
                    />
                  </div>
                </ng-container>
              </div>
						</div>
					</div>
        </li>
      </ul>
    </div>
    <div class="col-md-2 col-12 col-lg min-width-260px max-width-320px kanban-col"
      [class.drop-allowed]="isColAllowed('multiple-list-group-reviewing')"
      [class.drop-blocked]="isDragging && !isColAllowed('multiple-list-group-reviewing')">
      <ngb-alert [type]="'primary'" [dismissible]="false">
        <div class="alert-body text-center my-2">
          <strong>
            Chờ phê duyệt ({{ listReviewing?.length || 0 }})
          </strong>
        </div>
      </ngb-alert>

      <div class="kanban-intent" *ngIf="isDragging && isIntentAllowed('REVIEWING')">
        <span class="kanban-chip"
              [class.active]="hoverColId === 'multiple-list-group-reviewing'">
          {{ statusLabel('REVIEWING') }}
        </span>
      </div>
      <ul dragula="multiple-list-group" [dragulaModel]="listReviewing" class="draggable list-group min-height-100px max-height-70vh overflow-y-auto" id="multiple-list-group-reviewing">
        <li *ngFor="let item of listReviewing" 
            class="list-group-item custom-item-drag-drop mb-1 mt-1px" 
            [class.selected-work]="isWorkSelected(item)"
            (click)="onWorkItemClick($event, item)">
					<div class="card p-0 m-0 bg-transparent shadow-none">
						<div class="card-header p-0">
							<div *ngIf="item.class=='content'" class="badge badge-primary">
								<i data-feather="pen-tool" class="mr-25"></i>
								<span>Soạn thảo văn bản</span>
							</div>
							<div *ngIf="item.class=='qa'" class="badge badge-primary">
								<i data-feather="search" class="mr-25"></i>
								<span>Câu hỏi</span>
							</div>
						</div>
						
						<div class="card-body px-0 pb-0 pt-1">
							<h5>{{ item.title || 'Không có tiêu đề' }}</h5>

							<!-- Hiển thị lĩnh vực cho QA -->
							<div *ngIf="item.class === 'qa' && (item.field || item.fields)" class="mb-1">
								<small class="text-info">
									<i data-feather="tag" class="mr-25"></i>
									{{ getFieldName(item.field || item.fields) }}
								</small>
							</div>

							<div class="mt-1">
								<small class="text-muted d-block">Khởi tạo: {{ formatRelativeTime(item.created_at) }}</small>
								<small class="text-muted d-block">Cập nhật: {{ formatRelativeTime(item.updated_at) }}</small>
							</div>

              
              <div class="avatar-group d-flex justify-content-end">
                <ng-container *ngFor="let user of [item.writer, item.reviewer]">
                  <div
                    *ngIf="user"
                    data-toggle="tooltip"
                    data-popup="tooltip-custom"
                    data-placement="bottom"
                    placement="top"
                    container="body"
                    [ngbTooltip]="user.fullname"
                    class="avatar pull-up"
                  >
                    <img
                      [src]="user.avatar || 'assets/images/portrait/small/users.png'"
                      alt="Avatar"
                      width="24"
                      height="24"
                    />
                  </div>
                </ng-container>
              </div>
						</div>
					</div>
        </li>
      </ul>
    </div>
    <div class="col-md-2 col-12 col-lg min-width-260px max-width-320px kanban-col"
          [class.drop-allowed]="isColAllowed('multiple-list-group-completed')"
          [class.drop-blocked]="isDragging && !isColAllowed('multiple-list-group-completed')">
      <ngb-alert [type]="'success'" [dismissible]="false">
        <div class="alert-body text-center my-2">
          <strong>
            Hoàn thành ({{ listCompleted?.length || 0 }})
          </strong>
        </div>
      </ngb-alert>
      <div class="kanban-intent" *ngIf="isDragging && isIntentAllowed('COMPLETED')">
        <span class="kanban-chip"
              [class.active]="hoverColId === 'multiple-list-group-completed'">
          {{ statusLabel('COMPLETED') }}
        </span>
      </div>
      <ul dragula="multiple-list-group" [dragulaModel]="listCompleted" class="draggable list-group min-height-100px max-height-70vh overflow-y-auto" id="multiple-list-group-completed">
        <li *ngFor="let item of listCompleted" 
            class="list-group-item custom-item-drag-drop mb-1 mt-1px" 
            [class.selected-work]="isWorkSelected(item)"
            (click)="onWorkItemClick($event, item)">
					<div class="card p-0 m-0 bg-transparent shadow-none">
						<div class="card-header p-0">
							<div *ngIf="item.class=='content'" class="badge badge-primary">
								<i data-feather="pen-tool" class="mr-25"></i>
								<span>Soạn thảo văn bản</span>
							</div>
							<div *ngIf="item.class=='qa'" class="badge badge-primary">
								<i data-feather="search" class="mr-25"></i>
								<span>Câu hỏi</span>
							</div>
						</div>
						
						<div class="card-body px-0 pb-0 pt-1">
							<h5>{{ item.title || 'Không có tiêu đề' }}</h5>

							<!-- Hiển thị lĩnh vực cho QA -->
							<div *ngIf="item.class === 'qa' && (item.field || item.fields)" class="mb-1">
								<small class="text-info">
									<i data-feather="tag" class="mr-25"></i>
									{{ getFieldName(item.field || item.fields) }}
								</small>
							</div>

							<div class="mt-1">
								<small class="text-muted d-block">Khởi tạo: {{ formatRelativeTime(item.created_at) }}</small>
								<small class="text-muted d-block">Cập nhật: {{ formatRelativeTime(item.updated_at) }}</small>
							</div>

              
              <div class="avatar-group d-flex justify-content-end">
                <ng-container *ngFor="let user of [item.writer, item.reviewer]">
                  <div
                    *ngIf="user"
                    data-toggle="tooltip"
                    data-popup="tooltip-custom"
                    data-placement="bottom"
                    placement="top"
                    container="body"
                    [ngbTooltip]="user.fullname"
                    class="avatar pull-up"
                  >
                    <img
                      [src]="user.avatar || 'assets/images/portrait/small/users.png'"
                      alt="Avatar"
                      width="24"
                      height="24"
                    />
                  </div>
                </ng-container>
              </div>
						</div>
					</div>
        </li>
      </ul>
    </div>
    <div class="col-md-2 col-12 col-lg min-width-260px max-width-320px kanban-col"
          [class.drop-allowed]="isColAllowed('multiple-list-group-published')"
          [class.drop-blocked]="isDragging && !isColAllowed('multiple-list-group-published')">
      <ngb-alert [type]="'success'" [dismissible]="false">
        <div class="alert-body text-center my-2">
          <strong>
            Đã công bố ({{ listPublished?.length || 0 }})
          </strong>
        </div>
      </ngb-alert>
      <div class="kanban-intent" *ngIf="isDragging && isIntentAllowed('PUBLISHED')">
        <span class="kanban-chip"
              [class.active]="hoverColId === 'multiple-list-group-published'">
          {{ statusLabel('PUBLISHED') }}
        </span>
      </div>
      <ul dragula="multiple-list-group" [dragulaModel]="listPublished" class="draggable list-group min-height-100px max-height-70vh overflow-y-auto" id="multiple-list-group-published">
        <li *ngFor="let item of listPublished" 
            class="list-group-item custom-item-drag-drop mb-1 mt-1px" 
            [class.selected-work]="isWorkSelected(item)"
            (click)="onWorkItemClick($event, item)">
					<div class="card p-0 m-0 bg-transparent shadow-none">
						<div class="card-header p-0">
							<div *ngIf="item.class=='content'" class="badge badge-primary">
								<i data-feather="pen-tool" class="mr-25"></i>
								<span>Soạn thảo văn bản</span>
							</div>
							<div *ngIf="item.class=='qa'" class="badge badge-primary">
								<i data-feather="search" class="mr-25"></i>
								<span>Câu hỏi</span>
							</div>
						</div>
						
						<div class="card-body px-0 pb-0 pt-1">
							<h5>{{ item.title || 'Không có tiêu đề' }}</h5>

							<!-- Hiển thị lĩnh vực cho QA -->
							<div *ngIf="item.class === 'qa' && (item.field || item.fields)" class="mb-1">
								<small class="text-info">
									<i data-feather="tag" class="mr-25"></i>
									{{ getFieldName(item.field || item.fields) }}
								</small>
							</div>

							<div class="mt-1">
								<small class="text-muted d-block">Khởi tạo: {{ formatRelativeTime(item.created_at) }}</small>
								<small class="text-muted d-block">Cập nhật: {{ formatRelativeTime(item.updated_at) }}</small>
							</div>

              
              <div class="avatar-group d-flex justify-content-end">
                <ng-container *ngFor="let user of [item.writer, item.reviewer]">
                  <div
                    *ngIf="user"
                    data-toggle="tooltip"
                    data-popup="tooltip-custom"
                    data-placement="bottom"
                    placement="top"
                    container="body"
                    [ngbTooltip]="user.fullname"
                    class="avatar pull-up"
                  >
                    <img
                      [src]="user.avatar || 'assets/images/portrait/small/users.png'"
                      alt="Avatar"
                      width="24"
                      height="24"
                    />
                  </div>
                </ng-container>
              </div>
						</div>
					</div>
        </li>
      </ul>
    </div>
    <div class="col-md-2 col-12 col-lg min-width-260px max-width-320px  kanban-col"
          [class.drop-allowed]="isColAllowed('multiple-list-group-rejected')"
          [class.drop-blocked]="isDragging && !isColAllowed('multiple-list-group-rejected')">
      <ngb-alert [type]="'danger'" [dismissible]="false">
        <div class="alert-body text-center my-2">
          <strong>
            Bị từ chối ({{ listRejected?.length || 0 }})
          </strong>
        </div>
      </ngb-alert>
      <div class="kanban-intent" *ngIf="isDragging && isIntentAllowed('REJECTED')">
        <span class="kanban-chip"
              [class.active]="hoverColId === 'multiple-list-group-rejected'">
          {{ statusLabel('REJECTED') }}
        </span>
      </div>
      <ul dragula="multiple-list-group" [dragulaModel]="listRejected" class="draggable list-group min-height-100px max-height-70vh overflow-y-auto" id="multiple-list-group-rejected">
        <li *ngFor="let item of listRejected" 
            class="list-group-item custom-item-drag-drop mb-1 mt-1px" 
            [class.selected-work]="isWorkSelected(item)"
            (click)="onWorkItemClick($event, item)">
					<div class="card p-0 m-0 bg-transparent shadow-none">
						<div class="card-header p-0">
							<div *ngIf="item.class=='content'" class="badge badge-primary">
								<i data-feather="pen-tool" class="mr-25"></i>
								<span>Soạn thảo văn bản</span>
							</div>
							<div *ngIf="item.class=='qa'" class="badge badge-primary">
								<i data-feather="search" class="mr-25"></i>
								<span>Câu hỏi</span>
							</div>
						</div>
						
						<div class="card-body px-0 pb-0 pt-1">
							<h5>{{ item.title || 'Không có tiêu đề' }}</h5>

							<!-- Hiển thị lĩnh vực cho QA -->
							<div *ngIf="item.class === 'qa' && (item.field || item.fields)" class="mb-1">
								<small class="text-info">
									<i data-feather="tag" class="mr-25"></i>
									{{ getFieldName(item.field || item.fields) }}
								</small>
							</div>

							<div class="mt-1">
								<small class="text-muted d-block">Khởi tạo: {{ formatRelativeTime(item.created_at) }}</small>
								<small class="text-muted d-block">Cập nhật: {{ formatRelativeTime(item.updated_at) }}</small>
							</div>

              
              <div class="avatar-group d-flex justify-content-end">
                <ng-container *ngFor="let user of [item.writer, item.reviewer]">
                  <div
                    *ngIf="user"
                    data-toggle="tooltip"
                    data-popup="tooltip-custom"
                    data-placement="bottom"
                    placement="top"
                    container="body"
                    [ngbTooltip]="user.fullname"
                    class="avatar pull-up"
                  >
                    <img
                      [src]="user.avatar || 'assets/images/portrait/small/users.png'"
                      alt="Avatar"
                      width="24"
                      height="24"
                    />
                  </div>
                </ng-container>
              </div>
						</div>
					</div>
        </li>
      </ul>
    </div>
    <div class="col-md-2 col-12 col-lg pr-0 min-width-260px max-width-320px kanban-col"
          [class.drop-allowed]="isColAllowed('multiple-list-group-archived')"
          [class.drop-blocked]="isDragging && !isColAllowed('multiple-list-group-archived')">
      <ngb-alert [type]="'warning'" [dismissible]="false">
        <div class="alert-body text-center my-2">
          <strong>
            Đã lưu trữ ({{ listArchived?.length || 0 }})
          </strong>
        </div>
      </ngb-alert>
      <div class="kanban-intent" *ngIf="isDragging && isIntentAllowed('ARCHIVED')">
        <span class="kanban-chip"
              [class.active]="hoverColId === 'multiple-list-group-archived'">
          {{ statusLabel('ARCHIVED') }}
        </span>
      </div>
      <ul dragula="multiple-list-group" [dragulaModel]="listArchived" class="draggable list-group min-height-100px max-height-70vh overflow-y-auto" id="multiple-list-group-archived">
				<li *ngFor="let item of listArchived" 
            class="list-group-item custom-item-drag-drop mb-1 mt-1px" 
            [class.selected-work]="isWorkSelected(item)"
            (click)="onWorkItemClick($event, item)">
					<div class="card p-0 m-0 bg-transparent shadow-none">
						<div class="card-header p-0">
							<div *ngIf="item.class=='content'" class="badge badge-primary">
								<i data-feather="pen-tool" class="mr-25"></i>
								<span>Soạn thảo văn bản</span>
							</div>
							<div *ngIf="item.class=='qa'" class="badge badge-primary">
								<i data-feather="search" class="mr-25"></i>
								<span>Câu hỏi</span>
							</div>
						</div>
						
						<div class="card-body px-0 pb-0 pt-1">
							<h5>{{ item.title || 'Không có tiêu đề' }}</h5>

							<!-- Hiển thị lĩnh vực cho QA -->
							<div *ngIf="item.class === 'qa' && (item.field || item.fields)" class="mb-1">
								<small class="text-info">
									<i data-feather="tag" class="mr-25"></i>
									{{ getFieldName(item.field || item.fields) }}
								</small>
							</div>

							<div class="mt-1">
								<small class="text-muted d-block">Khởi tạo: {{ formatRelativeTime(item.created_at) }}</small>
								<small class="text-muted d-block">Cập nhật: {{ formatRelativeTime(item.updated_at) }}</small>
							</div>

              
              <div class="avatar-group d-flex justify-content-end">
                <ng-container *ngFor="let user of [item.writer, item.reviewer]">
                  <div
                    *ngIf="user"
                    data-toggle="tooltip"
                    data-popup="tooltip-custom"
                    data-placement="bottom"
                    placement="top"
                    container="body"
                    [ngbTooltip]="user.fullname"
                    class="avatar pull-up"
                  >
                    <img
                      [src]="user.avatar || 'assets/images/portrait/small/users.png'"
                      alt="Avatar"
                      width="24"
                      height="24"
                    />
                  </div>
                </ng-container>
              </div>
						</div>
					</div>
        </li>
      </ul>
    </div>
  </div>
</div>
    
<ng-template #modalContent let-modal>
  <form [formGroup]="contentForm" (ngSubmit)="submitContent()">
    <div class="modal-header">
      <h5 class="modal-title">{{ contentForm.get('id')?.value ? 'Cập nhật công việc' : 'Thêm công việc' }}</h5>
      <button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body" tabindex="0" ngbAutofocus>

      <!-- Trường Class (Loại việc) - PHẢI chọn với tạo mới (không có id) -->
      <div class="mb-2" *ngIf="!contentForm.get('id')?.value">
        <label for="workClass" class="form-label">Loại việc <span class="text-danger">*</span></label>
        <ng-select
          id="workClass"
          formControlName="class"
          [items]="[
            { label: 'Soạn thảo văn bản', value: 'content' },
            { label: 'Câu hỏi', value: 'qa' }
          ]"
          bindLabel="label"
          bindValue="value"
          placeholder="Chọn loại việc"
          [appendTo]="'body'"  
          [ngClass]="{'is-invalid': contentForm.get('class')?.invalid && contentForm.get('class')?.touched}"
        >
        </ng-select>
        <div *ngIf="contentForm.get('class')?.invalid && contentForm.get('class')?.touched" class="invalid-feedback">
          Loại việc là bắt buộc.
        </div>
      </div>

			<div class="mb-2" *ngIf="contentForm.get('id')?.value">
        <label class="form-label">Loại việc</label>
        <input type="text" class="form-control" [value]="contentForm.get('class')?.value === 'content' ? 'Soạn thảo văn bản' : (contentForm.get('class')?.value === 'qa' ? 'Câu hỏi' : '')" disabled>
      </div>

			<ng-container *ngIf="contentForm.get('id')?.value || contentForm.get('class')?.value">
        <div class="mb-2">
          <label for="workTitle" class="form-label">Tiêu đề <span class="text-danger">*</span></label>
          <input
            id="workTitle"
            type="text"
            class="form-control"
            placeholder="Nhập tiêu đề ..."
            formControlName="title"
            [attr.maxlength]="titleMax"
            (input)="enforceTitleLimit($event)"
            [ngClass]="{'is-invalid': contentForm.get('title')?.invalid && contentForm.get('title')?.touched}"
          >
          <small
            class="form-text text-muted"
            [class.text-danger]="titleCount >= titleMax"
            [style.opacity]="titleCount >= titleMax ? 1 : 0.7"
          >
            {{ titleCount }}/{{ titleMax }}
          </small>
          <div *ngIf="titleCount >= titleMax" class="invalid-feedback d-block">
            Đã đạt giới hạn {{ titleMax }} ký tự.
          </div>
          <div *ngIf="contentForm.get('title')?.errors?.['required'] && contentForm.get('title')?.touched"
              class="invalid-feedback d-block">
            Tiêu đề là bắt buộc.
          </div>
        </div>
        <ng-container [ngSwitch]="contentForm.get('class')?.value">

          <!-- Soạn thảo văn bản -->
          <ng-container *ngSwitchCase="'content'">
            <div class="mb-2">
              <label for="workDescription" class="form-label">Mô tả</label>
              <textarea id="workDescription" class="form-control" placeholder="Nhập mô tả ..." formControlName="description" rows="3"></textarea>
            </div>
            <!-- Thumbnail -->
            <div class="mb-2">
              <label class="form-label d-flex align-items-center justify-content-between">
                <span>
                  Ảnh bài viết (thumbnail)
                  <ng-container *ngIf="contentForm.get('class')?.value === 'content'">
                    <span class="text-danger">*</span>
                  </ng-container>
                </span>
                <small class="text-muted">PNG/JPG/WebP/GIF &le; 10MB</small>
              </label>

              <div class="thumb-uploader" [ngClass]="{'is-invalid': contentForm.get('image')?.invalid && contentForm.get('image')?.touched}">
                <div class="thumb-preview" *ngIf="thumbPreviewUrl; else pickBtn">
                  <img [src]="thumbPreviewUrl" alt="thumbnail" />
                  <div class="thumb-actions">
                    <button type="button" class="btn btn-sm btn-outline-secondary" (click)="triggerPickThumb()">Đổi ảnh</button>
                    <button type="button" class="btn btn-sm btn-outline-danger" (click)="removeThumb()">Xoá</button>
                  </div>
                </div>
                <ng-template #pickBtn>
                  <button type="button" class="btn btn-outline-primary" (click)="triggerPickThumb()">
                    <i data-feather="image" class="me-50"></i> Chọn ảnh…
                  </button>
                </ng-template>
                <input #thumbInput type="file" accept="image/*" class="d-none" (change)="onThumbFileSelected($event)" />
              </div>

              <!-- thông báo lỗi bắt buộc -->
              <div *ngIf="contentForm.get('image')?.invalid && contentForm.get('image')?.touched"
                  class="invalid-feedback d-block">
                Ảnh đại diện là bắt buộc.
              </div>
            </div>
            <div class="mb-2">
              <label class="form-label d-flex align-items-center justify-content-between">
                <span>Nội dung <span class="text-danger">*</span></span>
              </label>

              <div class="editor-wrap" [class.fullscreen]="isBodyFS" style="position: relative;">
                <div class="fs-head" *ngIf="isBodyFS">
                  <div class="fs-title">Soạn nội dung</div>
                  <div class="fs-actions">
                    <!-- <button type="button"
                            class="btn btn-sm btn-outline-secondary icon-btn"
                            (click)="toggleFS('body')"
                            ngbTooltip="Thu nhỏ">
                      <i data-feather="minimize-2"></i>
                    </button> -->
                  </div>
                </div>

                <!-- <button
                  type="button"
                  class="btn btn-sm btn-outline-secondary icon-btn"
                  (click)="toggleFS('body')"
                  [ngbTooltip]="isBodyFS ? 'Thu nhỏ' : 'Toàn màn hình'"
                  *ngIf="!isBodyFS">
                  <i [attr.data-feather]="isBodyFS ? 'minimize-2' : 'maximize-2'"></i>
                </button> -->

                <quill-editor
                  formControlName="body"
                  [modules]="editorModules"
                  [format]="'html'"
                  placeholder="Nhập nội dung tại đây..."
                  theme="snow"
                  class="bg-white editor-el"
                  (onEditorCreated)="onEditorCreated($event, 'body')"
                  [ngClass]="{ 'is-invalid': contentForm.get('body')?.invalid && contentForm.get('body')?.touched }"
                ></quill-editor>
              </div>

              <div *ngIf="contentForm.get('body')?.invalid && contentForm.get('body')?.touched" class="invalid-feedback d-block">
                Nội dung là bắt buộc
              </div>
            </div>
          </ng-container>

          <!-- Câu hỏi Q&A -->
          <ng-container *ngSwitchCase="'qa'">
            <div class="mb-2">
              <label class="form-label d-flex align-items-center justify-content-between">
                <span>Câu hỏi <span class="text-danger">*</span></span>
              </label>

              <div class="editor-wrap" [class.fullscreen]="isQuestionFS" style="position: relative;">
                <div class="fs-head" *ngIf="isQuestionFS">
                  <div class="fs-title">Soạn câu hỏi</div>
                  <div class="fs-actions">
                    <!-- <button type="button"
                            class="btn btn-sm btn-outline-secondary icon-btn"
                            (click)="toggleFS('question')"
                            ngbTooltip="Thu nhỏ">
                      <i data-feather="minimize-2"></i>
                    </button> -->
                  </div>
                </div>
                <!-- <button type="button"
                        class="btn btn-sm btn-outline-secondary icon-btn"
                        (click)="toggleFS('question')"
                        [ngbTooltip]="isQuestionFS ? 'Thu nhỏ' : 'Toàn màn hình'"
                        style="position:absolute; top:8px; right:8px; z-index:2;"
                        *ngIf="!isQuestionFS">
                  <i [attr.data-feather]="isQuestionFS ? 'minimize-2' : 'maximize-2'"></i>
                </button> -->
                <quill-editor
                  formControlName="question"
                  [modules]="editorModules"
                  [format]="'html'"
                  placeholder="Nhập câu hỏi..."
                  theme="snow"
                  class="bg-white editor-el"
                  (onEditorCreated)="onEditorCreated($event, 'question')"
                  (onBlur)="contentForm.get('question')?.markAsTouched()"
                  [ngClass]="{ 'is-invalid': contentForm.get('question')?.invalid && contentForm.get('question')?.touched }"
                ></quill-editor>
              </div>

              <div *ngIf="contentForm.get('question')?.invalid && contentForm.get('question')?.touched" class="invalid-feedback d-block">
                Câu hỏi là bắt buộc
              </div>
            </div>
            <div class="mb-2">
              <label class="form-label d-flex align-items-center justify-content-between">
                <span>Trả lời <span class="text-danger">*</span></span>
              </label>

              <div class="editor-wrap" [class.fullscreen]="isAnswerFS" style="position: relative;">
                <div class="fs-head" *ngIf="isAnswerFS">
                  <div class="fs-title">Soạn trả lời</div>
                  <div class="fs-actions">
                    <!-- <button type="button"
                            class="btn btn-sm btn-outline-secondary icon-btn"
                            (click)="toggleFS('answer')"
                            ngbTooltip="Thu nhỏ">
                      <i data-feather="minimize-2"></i>
                    </button> -->
                  </div>
                </div>
                <!-- <button type="button"
                        class="btn btn-sm btn-outline-secondary icon-btn"
                        (click)="toggleFS('answer')"
                        [ngbTooltip]="isAnswerFS ? 'Thu nhỏ' : 'Toàn màn hình'"
                        style="position:absolute; top:8px; right:8px; z-index:2;"
                        *ngIf="!isAnswerFS">
                  <i [attr.data-feather]="isAnswerFS ? 'minimize-2' : 'maximize-2'"></i>
                </button> -->
                <quill-editor
                  formControlName="answer"
                  [modules]="editorModules"
                  [format]="'html'"
                  placeholder="Nhập câu trả lời..."
                  theme="snow"
                  class="bg-white editor-el"
                  (onEditorCreated)="onEditorCreated($event, 'answer')"
                  (onBlur)="contentForm.get('answer')?.markAsTouched()"
                  [ngClass]="{ 'is-invalid': contentForm.get('answer')?.invalid && contentForm.get('answer')?.touched }"
                ></quill-editor>
              </div>

              <div *ngIf="contentForm.get('answer')?.invalid && contentForm.get('answer')?.touched" class="invalid-feedback d-block">
                Trả lời là bắt buộc
              </div>
            </div>
          </ng-container>
        </ng-container>

        <div class="mb-2 row" *ngIf="contentForm.get('class')?.value !== 'qa'">
          <div class="col-md-6">
            <label for="workSlug" class="form-label">
              Đường dẫn văn bản <span class="text-danger">*</span>
            </label>

            <input
              id="workSlug"
              type="text"
              class="form-control"
              placeholder="Chọn đường dẫn văn bản"
              formControlName="slug"
              [ngClass]="{
                'is-invalid':
                  (contentForm.get('slug')?.invalid && contentForm.get('slug')?.touched)
                  || contentForm.get('slug')?.errors?.['slugTaken']
              }"
            />

            <!-- thiếu slug -->
            <div
              *ngIf="contentForm.get('slug')?.errors?.['required'] && contentForm.get('slug')?.touched"
              class="invalid-feedback d-block">
              Đường dẫn văn bản là bắt buộc.
            </div>

            <!-- slug trùng -->
            <div
              *ngIf="contentForm.get('slug')?.errors?.['slugTaken']"
              class="invalid-feedback d-block"
              >
              Slug đã tồn tại.
              <button
                type="button"
                class="btn btn-link p-0 align-baseline"
                (click)="applySlugSuggestion()">
                Dùng gợi ý: {{ contentForm.get('slug')?.getError('suggest') }}
              </button>
            </div>
          </div>

          <div class="col-md-6">
            <label for="workKeywords" class="form-label">Từ khóa <span class="text-danger">*</span></label>
            <input
              id="workKeywords"
              type="text"
              class="form-control"
              placeholder="Chọn từ khóa"
              formControlName="keywords"
              [ngClass]="{
                'is-invalid': contentForm.get('keywords')?.invalid && contentForm.get('keywords')?.touched
              }"
            />
            <div
              *ngIf="contentForm.get('keywords')?.invalid && contentForm.get('keywords')?.touched"
              class="invalid-feedback">
              Từ khóa là bắt buộc.
            </div>
          </div>
        </div>

        <div *ngIf="contentForm.get('class')?.value === 'content'" class="mb-2">
          <label for="workType" class="form-label">Loại bài viết <span class="text-danger">*</span></label>
          <ng-select
            id="workType"
            formControlName="type"
            [items]="[
              { label: 'Tin tức', value: 'NORMAL' },
              { label: 'Nổi bật', value: 'HOT' }
            ]"
            bindLabel="label"
            bindValue="value"
            placeholder="Chọn loại bài viết"
            [clearable]="false"
            [ngClass]="{'is-invalid': contentForm.get('type')?.invalid && contentForm.get('type')?.touched}">
          </ng-select>
          <div *ngIf="contentForm.get('type')?.invalid && contentForm.get('type')?.touched" class="invalid-feedback">
            Loại bài viết là bắt buộc.
          </div>
        </div>
        <div class="mb-2" *ngIf="contentForm.get('class')?.value === 'content'">
          <label for="workPublishDate" class="form-label">Ngày công bố</label>
          <div class="input-group">
            <input
              class="form-control"
              placeholder="dd/mm/yyyy"
              formControlName="publish_date"
              ngbDatepicker
              #d="ngbDatepicker"
              [minDate]="todayNgb"
              readonly
            >
            <button class="btn btn-outline-secondary" (click)="d.toggle()" type="button">
              <i data-feather="calendar"></i>
            </button>
          </div>

          <!-- Thông báo lỗi nếu chọn ngày quá khứ -->
          <div
            *ngIf="contentForm.get('publish_date')?.errors?.['pastDate'] && contentForm.get('publish_date')?.touched"
            class="invalid-feedback d-block"
          >
            Ngày công bố không được ở quá khứ.
          </div>

          <small class="text-muted">
            • Để trống: hiển thị ngay khi bài ở trạng thái <code>PUBLISHED</code>.<br>
            • Chọn ngày: bài chỉ hiển thị trên Landing khi đến ngày này (và trạng thái là <code>PUBLISHED</code>).
          </small>
        </div>
        <div class="mb-2">
          <label for="workTopic" class="form-label">Chủ đề <span class="text-danger">*</span></label>
          <input
            id="workTopic"
            type="text"
            class="form-control"
            placeholder="Chọn chủ đề"
            formControlName="topic"
            [ngClass]="{'is-invalid': contentForm.get('topic')?.invalid && contentForm.get('topic')?.touched}"
          >
          <div *ngIf="contentForm.get('topic')?.invalid && contentForm.get('topic')?.touched" class="invalid-feedback">
            Chủ đề là bắt buộc.
          </div>
        </div>
        <div class="mb-2" *ngIf="contentForm.get('class')?.value === 'qa'">
  <label for="QAField" class="form-label">
    Lĩnh vực <span class="text-danger">*</span>
  </label>

  <select
    id="QAField"
    class="form-control"
    formControlName="field"
    [ngClass]="{
      'is-invalid':
        contentForm.get('field')?.invalid &&
        contentForm.get('field')?.touched
    }"
  >
    <option value="" disabled selected>-- Chọn lĩnh vực --</option>

    <option *ngFor="let field of qaFields" [value]="field.id">
      {{ field.name }}
    </option>
  </select>

  <div
    *ngIf="contentForm.get('field')?.invalid && contentForm.get('field')?.touched"
    class="invalid-feedback"
  >
    Lĩnh vực là bắt buộc.
  </div>
</div>

        <div class="mb-2 row">
          <div class="col-md-6">
            <label for="workWriter" class="form-label">Người viết <span class="text-danger">*</span></label>
            <ng-select
              id="workWriter"
              formControlName="writer"
              [items]="writerOptions"
              [bindLabel]="'user.fullname'"
              [bindValue]="'user.id'"
              placeholder="Chọn người viết"
              [ngClass]="{'is-invalid': contentForm.get('writer')?.invalid && contentForm.get('writer')?.touched}"
            >
            </ng-select>
            <div *ngIf="contentForm.get('writer')?.invalid && contentForm.get('writer')?.touched" class="invalid-feedback">
              Người viết là bắt buộc.
            </div>
          </div>
          <div class="col-md-6">
            <label for="workReviewer" class="form-label">Người phê duyệt</label>
            <ng-select
              id="workReviewer"
              formControlName="reviewer"
              [items]="reviewerUsers"
              [bindLabel]="'user.fullname'"
              [bindValue]="'user.id'"
              placeholder="Chọn reviewer (có thể bỏ trống)"
              [clearable]="true"
              [ngClass]="{'is-invalid': contentForm.get('reviewer')?.invalid && contentForm.get('reviewer')?.touched}"
            >
            </ng-select>
          </div>
          <!-- <div class="col-md-4" *ngIf="!isQA">
            <label for="workAdmin" class="form-label">Admin</label>
            <ng-select
              id="workAdmin"
              formControlName="admin"
              [items]="adminUsers"
              [bindLabel]="'user.fullname'"
              [bindValue]="'user.id'"
              placeholder="Chọn admin (có thể bỏ trống)"
              [clearable]="true"
              [ngClass]="{'is-invalid': contentForm.get('admin')?.invalid && contentForm.get('admin')?.touched}"
            >
            </ng-select>
          </div> -->
        </div>
      </ng-container>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-outline-secondary" (click)="modal.dismiss('Close click')">Hủy</button>
      <button
        type="submit"
        class="btn btn-primary"
        [disabled]="contentForm.invalid"
        rippleEffect
      >
        {{ contentForm.get('id')?.value ? 'Cập nhật' : 'Thêm mới' }}
      </button>
    </div>
  </form>
</ng-template>
<ng-template #VideoUrlModal let-modal>
  <div class="modal-header">
    <h5 class="modal-title">Nhúng video bằng URL</h5>
    <button type="button" class="close" (click)="modal.dismiss()"><span aria-hidden="true">&times;</span></button>
  </div>
  <div class="modal-body">
    <label class="form-label">Dán URL (YouTube/Vimeo hoặc link .mp4/.webm/.ogg)</label>
    <input type="text" class="form-control" [(ngModel)]="tmpVideoUrl"
           placeholder="https://youtu.be/..., https://vimeo.com/..., https://.../video.mp4">
    <small class="text-muted d-block mt-1">
      • URL YouTube/Vimeo sẽ chèn <code>&lt;iframe&gt;</code>.  
      • URL file (.mp4/.webm/.ogg) sẽ chèn <code>&lt;video&gt;</code>.
    </small>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-outline-secondary" (click)="modal.dismiss()">Hủy</button>
    <button type="button" class="btn btn-primary" (click)="confirmVideoUrl(modal)">Chèn</button>
	</div>
</ng-template>

<!-- Modal gán Writer/Reviewer -->
<ng-template #AssignModal let-modal>
	<div class="modal-header">
		<h5 class="modal-title">Gán {{ assignModalType === 'writer' ? 'người viết' : 'người phê duyệt' }}</h5>
		<button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>

	<div class="modal-body">
		<div class="mb-1">
			<label class="form-label">Chọn {{ assignModalType === 'writer' ? 'người viết' : 'người phê duyệt' }}</label>
			<ng-select
				[items]="assignModalType === 'writer' ? writerOptions  : reviewerUsers"
				[bindLabel]="'user.fullname'"
				[bindValue]="'user.id'"
				placeholder="Chọn {{ assignModalType === 'writer' ? 'người viết' : 'người phê duyệt' }}"
				[(ngModel)]="selectedUserId"
				[clearable]="true">
			</ng-select>
		</div> 

    <strong class="text-dark">{{ selectedWork.length }} Thẻ sẽ được gán:</strong>
		<!-- <div class="alert alert-primary"> -->
			<ol type="1" class="py-1">
				<li *ngFor="let work of selectedWork" class="mb-25">
          <div *ngIf="work.class=='content'" class="badge badge-primary">
            <i data-feather="pen-tool" class="mr-25"></i>
            <span>Soạn thảo văn bản</span>
          </div>
          <div *ngIf="work.class=='qa'" class="badge badge-primary">
            <i data-feather="search" class="mr-25"></i>
            <span>Câu hỏi</span>
          </div>
					<span class="text-dark ml-25">{{ work.title || 'Không có tiêu đề' }}</span>
				</li>
			</ol>
		<!-- </div> -->
	</div>
	<div class="modal-footer">
		<button type="button" class="btn btn-outline-secondary" (click)="modal.dismiss('Close click')">Hủy</button>
		<button 
			type="button" 
			class="btn btn-primary" 
			[disabled]="!selectedUserId"
			(click)="assignUserToSelectedWork(selectedUserId); modal.close('Assign click')">
			Gán {{ assignModalType === 'writer' ? 'người viết' : 'người phê duyệt' }}
		</button>
	</div>
</ng-template>

<ng-template #CmsDashboardModal let-modal>
	<div class="modal-header">
		<h5 class="modal-title">Thống kê</h5>
		<button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="modal-body" tabindex="0" ngbAutofocus>
		<app-cms-dashboard
			[cmsData]="cmsData"
			[writerUsers]="writerUsers"
			[reviewerUsers]="reviewerUsers"
			[adminUsers]="adminUsers"
		></app-cms-dashboard>
	</div>
	<!-- <div class="modal-footer">
		<button type="button" class="btn btn-primary"
			(click)="modal.close('Accept click')">Accept</button>
	</div> -->
</ng-template>

<ng-template #CmsUserListModal let-modal>
	<div class="modal-header">
		<h5 class="modal-title">Danh sách người dùng CMS</h5>
		<button type="button" class="close" (click)="closeModal()" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div class="modal-body" tabindex="0" ngbAutofocus>
		<app-cms-role></app-cms-role>
	</div>
	<!-- <div class="modal-footer">
		<button type="button" class="btn btn-primary"
			(click)="modal.close('Accept click')">Accept</button>
	</div> -->
</ng-template>