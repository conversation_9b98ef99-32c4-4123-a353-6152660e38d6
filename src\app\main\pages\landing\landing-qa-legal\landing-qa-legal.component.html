
  <app-landing-header
    [activeSection]="activeSection"
    [showNav]="true"
    [logoSrc]="'assets/images/logo/COpenAIlogo.svg'"
    brandText="CLS"
    (sectionSelect)="scrollTo($event)">
  </app-landing-header>
<div class="container">
  <aside class="sidebar">
    <div class="sb-subheader">Lĩnh vực câu hỏi</div>
    <ul class="sb-list" id="categoryList">
      <li class="active" 
          *ngFor="let cat of categories" 
          [class.active]="cat.id === currentCategory"
          (click)="setCategory(cat.id)">
        {{ cat.name }} ({{ cat.count }})
        <span class="sb-desc">{{ cat.description }}</span>
      </li>
    </ul>
  </aside>

  <main class="main-content">
    <div class="mc-header">
      <div class="mc-title">{{ pageTitle }}</div>
      <div class="search-wrap">
        <input 
          type="text" 
          class="search-inp" 
          placeholder="T<PERSON><PERSON> kiếm theo mã câu hỏi hoặc từ khóa"
          [(ngModel)]="searchQuery"
          (keydown.enter)="triggerSearch()"
          (blur)="triggerSearch()">
        <span class="search-icon" (click)="triggerSearch()">🔍</span>
      </div>
    </div>

    <div class="qa-list">
      <!-- <div *ngIf="isLoading" class="loading">
        Đang tải dữ liệu...
      </div> -->

      <div *ngIf="!isLoading && paginatedData.length === 0" class="no-results">
        Không tìm thấy kết quả. Hãy thử từ khóa khác hoặc chọn lĩnh vực khác.
      </div>

      <div
        *ngFor="let item of paginatedData"
        class="qa-item"
        (click)="openModal(item)">
        <div class="qa-id">#{{ item.id }}</div>
        <div class="qa-body">
          <div class="qa-title">{{ item.title }}</div>
          <div class="qa-meta">
            {{ getFieldName(item.fields) }}
            <span class="qa-date">{{ item.updated_at | date:'dd/MM/yyyy' }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="mc-footer">
      <div class="page-size-wrap">
        Hiển thị:
        <select class="page-select" [(ngModel)]="pageSize" (change)="onPageSizeChange()">
          <option [value]="15">15 kết quả</option>
          <option [value]="20">20 kết quả</option>
          <option [value]="50">50 kết quả</option>
        </select>
      </div>
      <div class="pagination">
        <button 
          *ngFor="let page of paginationPages" 
          class="pg-btn" 
          [class.active]="page.value === currentPage"
          [disabled]="page.disabled"
          (click)="goToPage(page.value)">
          {{ page.label }}
        </button>
      </div>
    </div>
  </main>
</div>
  <button
    type="button"
    class="landing__to-top"
    *ngIf="showBackToTop"
    (click)="scrollToTop()"
    aria-label="Lên đầu trang"
    title="Lên đầu trang">
    <svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true">
      <path d="M12 5l-7 7h4v7h6v-7h4z" fill="currentColor"></path>
    </svg>
  </button>
<app-landing-footer [url1]="urls?.url1"></app-landing-footer>
<!-- Modal -->
<div class="modal" [class.show]="showModal" (click)="closeModal()">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <span class="modal-close" (click)="closeModal()">&times;</span>
    <div class="modal-body">
      <h2 class="m-title">{{ selectedItem?.title }}</h2>
      
      <div class="m-question-box">
        <span class="m-q-label">❓ CÂU HỎI:</span>
        <div class="m-q-text" [innerHTML]="selectedItem?.question"></div>

      </div>

      <div class="m-answer-area">
        <h3>Trả lời:</h3>
        <div class="m-answer-box">
          <div class="m-a-text" [innerHTML]="selectedItem?.answer"></div>
        </div>
      </div>
    </div>
  </div>
</div>