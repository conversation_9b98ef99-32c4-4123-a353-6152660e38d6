import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  ElementRef,
  Input,
  NgZone,
  OnInit,
  ViewChild,
  ViewEncapsulation,
  Renderer2,
} from "@angular/core";
import { Dom<PERSON>anitizer, SafeHtml } from "@angular/platform-browser";
import { Activated<PERSON>oute, Router } from "@angular/router";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ColumnMode } from "@swimlane/ngx-datatable";
import { BoSungVanBanDieuKhoanService } from "app/main/apps/quan-ly-van-ban/detail-work-space/bo-sung-van-ban-dieu-khoan/bo-sung-van-ban-dieu-khoan.service";
import { ChatbotService } from "app/main/apps/quan-ly-van-ban/detail-work-space/chatbot/chatbot.service";
import { DetailWorkSpaceService } from "app/main/apps/quan-ly-van-ban/detail-work-space/detail-work-space.service";
import { ListDocumentService } from "app/main/apps/quan-ly-van-ban/detail-work-space/list-document/list-document.service";
import { TakeNoteService } from "app/main/apps/quan-ly-van-ban/detail-work-space/take-note/take-note.service";
import { FormType } from "app/models/FormType";
import { ShowContent } from "app/models/ShowContent";
import { ShowSideBar } from "app/models/ShowSideBa";
import { environment } from "environments/environment";
import { FlatpickrOptions } from "ng2-flatpickr";
import { NgxExtendedPdfViewerComponent } from "ngx-extended-pdf-viewer";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { take, takeUntil, finalize } from "rxjs/operators";
import Swal from "sweetalert2";
import { ViewDetailFileService } from "./view-detail-file.service";
import { NgbPanelChangeEvent } from "@ng-bootstrap/ng-bootstrap";
import { cleanWordHtml } from "../../../../../util/word-html-cleaner";

@Component({
  selector: "app-view-detail-file",
  templateUrl: "./view-detail-file.component.html",
  styleUrls: ["./view-detail-file.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class ViewDetailFileComponent implements OnInit {
  @ViewChild("sumarizeContent")
  sumarizeContent!: ElementRef<HTMLParagraphElement>;
  @ViewChild("soHieuRef") soHieuRef!: ElementRef<HTMLTableCellElement>;
  @ViewChild("contentDiv") contentDiv!: ElementRef;
  @ViewChild("addRelateDocumentModal") addRelateDocumentModal!: any;
  @ViewChild("referenceModal") referenceModal: any;
  @ViewChild("relationModal") relationModal: any;
  @ViewChild(NgxExtendedPdfViewerComponent)
  pdfViewer: NgxExtendedPdfViewerComponent;
  @Input("modal") public modal: NgbActiveModal;
  @Input("type") public type: FormType;
  @Input() hideChartTab: boolean = false;
  @Input() hideSaveButton: boolean = false;
  @Input() showCloseButton = false;
  public isShowTable: boolean = false;
  public ColumnMode = ColumnMode;
  public isChatbotDraftView: boolean = false;
  public sizePage = [10, 20, 30, 100];
  public limit: number = this.sizePage[0];
  public avtiveTab = "";
  public fileName = "";
  public fileInfor;
  public fileId: string = "";
  public esId: string = "";
  public dataFile: any;
  public dataReference: any;
  public parentRoute: string = "/";
  public graphData: any = null;
  public graphNotFound: boolean = false;
  public _unSubscribe: Subject<any> = new Subject<any>();

  // Graph form state management
  public graphFormState = {
    search_legal_term: "VAN_BAN",
    isSearchAdvance: false,
    selectedBoLocMoiQuanHe: [],
    selectedTinhTrangHieuLuc: [],
    selectedBoLocLoaiVanBan: [],
    selectedCoQuanBanHanh: [],
    depth: 1,
    global_limit: 100,
    limit_per_seed: 100,
    dateFilterMode: null,
    dateFilterFrom: null,
    dateFilterTo: null,
    depthError: "",
    global_limitError: "",
    limit_per_seedError: "",
  };

  private lastQueryState:
    | {
      fileId: string | null;
      tabs: string | null;
      type: string | null;
      es_id: string | null;
      luocdo: string | null;
      openMode: string | null;
      fileName: string | null;
      save: any;
    }
    | null = null;
  private navHistory: any[] = [];        // stack các state trước đó
  private lastQueryParams: any = null;   // lưu state hiện tại
  private isNavigatingBack = false;
  private isRestoringTabFromHistory = false;
  // Track current document ID to detect document changes
  private currentDocumentId: string = null;
  // Track luoc do calls to prevent duplicates
  private currentLuocDoId: string | null = null;
  private luocDoInFlight: Set<string> = new Set();

  // Graph data cache - scoped by document ID to preserve state when switching tabs
  private cachedGraphData: Map<string, any> = new Map();
  private cachedGraphDocumentId: string = null;

  // Graph UI state cache - preserve hidden nodes, positions, and panel states per document
  private cachedGraphUIState: Map<string, {
    hiddenNodeIds: Set<string>;
    previousNodePositions: Map<string, { x: number; y: number }>;
    documentListExpanded: boolean;
    showDocumentTable: boolean;
  }> = new Map();
  isLoading: boolean = true;
  isFetchingContent: boolean = false;
  isFetchingContentModal: boolean = false;
  listLoading: any[] = [];
  isLoadingGraph: boolean = false;
  isSavingToListDocument: boolean = false;
  refToDifferentDocument: boolean = false;
  relRefToDifferentDocument: boolean = false;
  listReferences: any[] = [];
  titleModalReference: string = "";
  storedTooltipId: string = null;
  listRelations: any[] = [];
  relationTitleModal: string = null;
  relationType: string = null;
  public listVanBanCanCu: any[] = [];
  public listVanBanBiBaiBo: any[] = [];
  public listVanBanBiBaiBoMotPhan: any[] = [];
  public listVanBanBiDinhChi: any[] = [];
  public listVanBanBiDinhChiMotPhan: any[] = [];
  public listVanBanBiDinhChinh: any[] = [];
  public listVanBanBiHuyBo: any[] = [];
  public listVanBanBiHuyBoMotPhan: any[] = [];
  public listVanBanBiThayThe: any[] = [];
  public listVanBanBiThayTheMotPhan: any[] = [];
  public listVanBanChuaXacDinh: any[] = [];
  public listVanBanChuaXacDinhMotPhan: any[] = [];
  public listVanBanDanChieu: any[] = [];
  public listVanBanDuocHuongDan: any[] = [];
  public listVanBanQuyDinhChiTiet: any[] = [];
  public listVanBanDuocSuaDoi: any[] = [];
  public listVanBanDuocSuaDoiBoSung: any[] = [];
  public listVanBanLienQuanKhac: any[] = [];
  public listVanBanLienQuan: any[] = [];
  public listVanBanLienQuanFilter: any[] = [];
  public listVanBanBaiBo: any[] = [];
  public listVanBanBiHetHieuLuc: any[] = [];
  public listVanBanBiHetHieuLucMotPhan: any[] = [];
  public listVanBanDinhChi: any[] = [];
  public listVanBanDinhChiMotPhan: any[] = [];
  public listVanBanDuocQuyDinhChiTiet: any[] = [];
  public listVanBanHuongDan: any[] = [];
  public listVanBanHetHieuLuc: any[] = [];
  public listVanBanHetHieuLucMotPhan: any[] = [];
  public listVanBanHuyBo: any[] = [];
  public listVanBanHuyBoMotPhan: any[] = [];
  public listVanBanSuaDoi: any[] = [];
  public listVanBanSuaDoiBoSung: any[] = [];
  public listVanBanThayThe: any[] = [];
  public listVanBanThayTheMotPhan: any[] = [];
  public isToggleAddClause: boolean = false;
  public typeDocument: string;
  public listPanels = [
    { title: "Văn bản căn cứ", list: this.listVanBanCanCu },
    { title: "Văn bản bị bãi bỏ", list: this.listVanBanBiBaiBo },
    {
      title: "Văn bản bị bãi bỏ một phần",
      list: this.listVanBanBiBaiBoMotPhan,
    },
    { title: "Văn bản bị đình chỉ", list: this.listVanBanBiDinhChi },
    {
      title: "Văn bản bị đình chỉ một phần",
      list: this.listVanBanBiDinhChiMotPhan,
    },
    { title: "Văn bản bị đính chính", list: this.listVanBanBiDinhChinh },
    { title: "Văn bản bị huỷ bỏ", list: this.listVanBanBiHuyBo },
    {
      title: "Văn bản bị huỷ bỏ một phần",
      list: this.listVanBanBiHuyBoMotPhan,
    },
    { title: "Văn bản bị thay thế", list: this.listVanBanBiThayThe },
    {
      title: "Văn bản bị thay thế một phần",
      list: this.listVanBanBiThayTheMotPhan,
    },
    { title: "Văn bản chưa xác định", list: this.listVanBanChuaXacDinh },
    {
      title: "Văn bản chưa xác định một phần",
      list: this.listVanBanChuaXacDinhMotPhan,
    },
    { title: "Văn bản dẫn chiếu", list: this.listVanBanDanChieu },
    { title: "Văn bản được hướng dẫn", list: this.listVanBanDuocHuongDan },
    { title: "Văn bản quy định chi tiết", list: this.listVanBanQuyDinhChiTiet },
    { title: "Văn bản được sửa đổi", list: this.listVanBanDuocSuaDoi },
    {
      title: "Văn bản được sửa đổi bổ sung",
      list: this.listVanBanDuocSuaDoiBoSung,
    },
    { title: "Văn bản liên quan khác", list: this.listVanBanLienQuanKhac },

    { title: "Văn bản bãi bỏ", list: this.listVanBanBaiBo },
    { title: "Văn bản bị hết hiệu lực", list: this.listVanBanBiHetHieuLuc },
    {
      title: "Văn bản bị hết hiệu lực một phần",
      list: this.listVanBanBiHetHieuLucMotPhan,
    },
    { title: "Văn bản đình chỉ", list: this.listVanBanDinhChi },
    { title: "Văn bản đình chỉ một phần", list: this.listVanBanDinhChiMotPhan },
    {
      title: "Văn bản được quy định chi tiết",
      list: this.listVanBanDuocQuyDinhChiTiet,
    },
    { title: "Văn bản hướng dẫn", list: this.listVanBanHuongDan },
    { title: "Văn bản hết hiệu lực", list: this.listVanBanHetHieuLuc },
    {
      title: "Văn bản hết hiệu lực một phần",
      list: this.listVanBanHetHieuLucMotPhan,
    },
    { title: "Văn bản huỷ bỏ", list: this.listVanBanHuyBo },
    { title: "Văn bản huỷ bỏ một phần", list: this.listVanBanHuyBoMotPhan },
    { title: "Văn bản sửa đổi", list: this.listVanBanSuaDoi },
    { title: "Văn bản sửa đổi bổ sung", list: this.listVanBanSuaDoiBoSung },
    { title: "Văn bản thay thế một phần", list: this.listVanBanThayTheMotPhan },
  ];
  public FormType = FormType; // CREATE là truòng hợp xem văn bản từ mục chuyển đổi văn bản
  isMaximized = false;
  private _destroy$ = new Subject<void>();
  public listPanelsFiltered: any = [];
  public activePanelIds = [];
  public activePanelModalIds: string[] = [];
  public isEditTongQuan: boolean = false;
  safeHtml: SafeHtml;
  safeHtmlWithReference: SafeHtml;
  referenceContentForModal: SafeHtml;
  public statusSummarize;
  public luocdo: string;
  public isEditSumarize: boolean = false;
  public valueSumarizeEdited: string = "";
  public customDateOptions1: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
  };
  public customDateOptions2: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
  };
  public customDateOptions3: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
  };
  public listLoaiVanBan: any = [
    {
      label: "Quyết định",
      value: "Quyết định",
    },
    {
      label: "Thông tư",
      value: "Thông tư",
    },
    {
      label: "Nghị quyết",
      value: "Nghị quyết",
    },
    {
      label: "Nghị định",
      value: "Nghị định",
    },
    {
      label: "Thông tư liên tịch",
      value: "Thông tư liên tịch",
    },

    {
      label: "Luật",
      value: "Luật",
    },
    {
      label: "Văn bản hợp nhất",
      value: "Văn bản hợp nhất",
    },
    {
      label: "Pháp lệnh",
      value: "Pháp lệnh",
    },
    {
      label: "Công văn",
      value: "Công văn",
    },
    {
      label: "Bộ luật",
      value: "Bộ luật",
    },
    {
      label: "Nghị quyết liên tịch",
      value: "Nghị quyết liên tịch",
    },
    {
      label: "Chỉ thị",
      value: "Chỉ thị",
    },
    {
      label: "Văn bản khác",
      value: "Văn bản khác",
    },
    {
      label: "Lệnh",
      value: "Lệnh",
    },
    {
      label: "Hiến pháp",
      value: "Hiến pháp",
    },
    {
      label: "Văn bản liên quan",
      value: "Văn bản liên quan",
    },
    {
      label: "Thông báo",
      value: "Thông báo",
    },
    {
      label: "Chương trình",
      value: "Chương trình",
    },
    {
      label: "Sắc lệnh",
      value: "Sắc lệnh",
    },
    {
      label: "Thông tư liên bộ",
      value: "Thông tư liên bộ",
    },
    {
      label: "Hiệp định",
      value: "Hiệp định",
    },
    {
      label: "Sắc luật",
      value: "Sắc luật",
    },
  ];
  public isStreaming: boolean = false;
  public dataSummarize: string = "";
  public save: string = "true"; // để dùng cho trường hợp xem tài liệu được lưu từ tìm kiếm về, k hiển thị nút lưu
  public clauseId: string;
  public loadFileError: boolean = false;
  public streamingDone: boolean = false;
  public statusStreaming: number = 0;
  private tooltipEl: HTMLElement | null = null;
  hoverPopupVisible = false;
  popupContent: SafeHtml = "";
  popupPosition = { x: 0, y: 0 };
  docChosen?: string = null;
  private referenceListeners: Function[] = [];
  private relationListeners: Function[] = [];
  constructor(
    private route: ActivatedRoute,
    private viewDetailFile: ViewDetailFileService,
    private router: Router,
    private documentService: ListDocumentService,
    private toast: ToastrService,
    private sanitizer: DomSanitizer,
    private bosungVanBan: BoSungVanBanDieuKhoanService,
    private workspace: DetailWorkSpaceService,
    private chatbotService: ChatbotService,
    private ngZone: NgZone,
    private noteService: TakeNoteService,
    private cdr: ChangeDetectorRef,
    private modalService: NgbModal,
    private elRef: ElementRef,
    private renderer: Renderer2
  ) { }

  ngOnInit(): void {
    if (!window.location.href.includes('workspace')) {
      document.body.classList.add('apply-height-limit');
    }
    this.viewDetailFile.graphData
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((data) => {
        // Only update if data exists and matches current document
        if (data) {
          const currentGraphId =
            this.typeDocument === "searching"
              ? this.fileId
              : this.esId || this.fileId;
          if (currentGraphId) {
            // Update cache when child component updates graphData
            this.cachedGraphData.set(currentGraphId, data);
            this.cachedGraphDocumentId = currentGraphId;
          }
        }
        this.graphData = data;
      });

    this.route.queryParams
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((params) => {
        const currentParams = { ...params };

        // Handle explicit graph reset request (e.g., from workspace "Đang tìm kiếm" panel)
        if (params["resetGraph"] === "1") {
          this.cachedGraphData.clear();
          this.cachedGraphDocumentId = null;
          this.cachedGraphUIState.clear();
          this.resetGraphFormState();
          this.graphData = null;
          this.graphNotFound = false;

          // Remove the flag so it only applies once
          this.router.navigate([], {
            relativeTo: this.route,
            queryParams: { resetGraph: null },
            queryParamsHandling: "merge",
            replaceUrl: true,
          });
        }

        if (
          !this.isNavigatingBack &&                 // chỉ push khi không phải đang bấm nút back
          this.lastQueryParams &&                   // đã có state trước đó
          this.lastQueryParams.fileId               // state trước đó đang xem 1 văn bản
        ) {
          // lưu lại bước trước vào stack
          this.navHistory.push({ ...this.lastQueryParams });
        }

        // cập nhật state hiện tại & reset cờ
        this.lastQueryParams = currentParams;
        this.isNavigatingBack = false;
        this.fileId = params["fileId"];
        this.avtiveTab = params["tabs"];
        this.typeDocument = params["type"];

        this.esId =
          this.typeDocument === "searching" ? "" : params["es_id"] || "";
        this.luocdo = params["luocdo"];
        this.save = params["save"];

        this.loadFileError = false;
        this.isLoading = true;
        this.isEditTongQuan = false;
        this.fileName = params["fileName"];
        this.dataSummarize = "";
        // Reset dữ liệu tóm tắt khi đổi tài liệu
        this.isEditSumarize = false;

        if (this.dataFile?.docx_url) {
          this.dataFile.docx_url = null;
        }

        if (this.fileId && this.fileId !== this.currentDocumentId) {
          this.cachedGraphData.clear();
          this.cachedGraphDocumentId = null;
          this.cachedGraphUIState.clear();
          // Reset luoc do tracking when document changes
          this.currentLuocDoId = null;
        }
        if (this.fileId && this.fileId !== this.currentDocumentId) {
          this.resetGraphFormState();
          this.currentDocumentId = this.fileId;
        }

        if (this.fileId) {
          if (params["type"] == "upload") {
            this.getDetailFile(true);
          } else {
            this.getDetailFileFromSearch(true);
          }
        }
        this.getLuocDo();
      });
    this.workspace.isMaximized
      .pipe(takeUntil(this._destroy$))
      .subscribe(val => {
        this.isMaximized = val;
      });

    this.viewDetailFile.fileInfor
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        this.fileInfor = res;
      });
    this.viewDetailFile.clauseId // là id của điều từ chatbot
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        this.clauseId = res;
      });

    this.viewDetailFile.isSaveFileFromSearch
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        if (res) {
          this.getLuocDo();
        }
      });
  }
  ngAfterViewInit() {
    this.viewDetailFile.clauseId2 // là id của điều từ danh sáhc văn bản
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        this.clauseId = res;
        // Only change to "toanvan" tab if there's actually a clause ID to show
        if (res && this.avtiveTab !== "toanvan" && this.avtiveTab !== "toanvandanchieu") {
          this.avtiveTab = "toanvan";
          this.cdr?.detectChanges(); // ép chạy CD ngay sau khi đổi tab
        }
        // 1) Chờ Angular stable
        this.ngZone.onStable.pipe(take(1)).subscribe(() => {
          // 2) Chờ browser paint ở frame kế tiếp rồi mới scroll
          requestAnimationFrame(() => {
            this.scrollToClause(res);
          });
        });
      });
  }

  isInsideWorkspace(): boolean {
    return this.router.url.includes("/quan-ly-van-ban/workspace");
  }

  private getActionTarget(event: MouseEvent): {
    type: 'dan_chieu' | 'xac_dinh_quan_he' | null;
    el: HTMLElement | null;
  } {
    const target = event.target as HTMLElement;

    const danChieu = target.closest('a[task="dan_chieu"]');
    if (danChieu) {
      return { type: 'dan_chieu', el: danChieu as HTMLElement };
    }

    const xacDinh = target.closest('button#xac_dinh_quan_he');
    if (xacDinh) {
      return { type: 'xac_dinh_quan_he', el: xacDinh as HTMLElement };
    }

    return { type: null, el: null };
  }

  toanvanMouseOver(event: MouseEvent) {
    if (!this.dataFile.html_with_reference) return;

    const { type, el } = this.getActionTarget(event);
    if (!el) return;

    switch (type) {
      case 'dan_chieu':
        this.showTooltip(event, el);
        break;

      case 'xac_dinh_quan_he':
        this.showTooltipBtnRelation(event, el);
        break;
    }
  }

  toanvanMouseOut(event: MouseEvent) {
    if (!this.dataFile.html_with_reference) return;

    const { type, el } = this.getActionTarget(event);
    if (!el) return;

    if (el.contains(event.relatedTarget as Node)) return;

    this.hideTooltip();
  }

  toanvanClick(event: MouseEvent) {
    if (!this.dataFile.html_with_reference) return;

    const { type, el } = this.getActionTarget(event);
    if (!el) return;

    event.preventDefault();
    event.stopPropagation();

    switch (type) {
      case 'dan_chieu':
        this.handleShowReference(el);
        break;

      case 'xac_dinh_quan_he':
        this.handleShowRelation(el, this.getContentTooltip(el.className, el.getAttribute("tooltip_id")));
        break;
    }
  }

  bindReferenceLinks() {
    this.referenceListeners.forEach(unsub => unsub());
    this.referenceListeners = [];

    const links = this.elRef.nativeElement.querySelectorAll('a[task="dan_chieu"]');
    links.forEach((link: HTMLElement) => {
      // click
      this.referenceListeners.push(
        this.renderer.listen(link, "click", () => { this.handleShowReference(link); })
      );

      // hover in
      this.referenceListeners.push(
        this.renderer.listen(link, "mouseenter", (event) => { this.showTooltip(event, link); })
      );

      // hover out
      this.referenceListeners.push(
        this.renderer.listen(link, "mouseleave", () => { this.hideTooltip(); })
      );
    });

    // console.log("Bind reference links completed.");
  }

  bindRelationButtons() {
    this.relationListeners.forEach(unsub => unsub());
    this.relationListeners = [];

    const buttons = this.elRef.nativeElement.querySelectorAll('button[id="xac_dinh_quan_he"]');
    buttons.forEach((button: HTMLElement) => {
      // click
      this.relationListeners.push(
        this.renderer.listen(button, "click", () => {
          this.handleShowRelation(button, this.getContentTooltip(button.className, button.getAttribute("tooltip_id")));
        })
      );

      // hover in
      this.relationListeners.push(
        this.renderer.listen(button, "mouseenter", (event) => { this.showTooltipBtnRelation(event, button); })
      );

      // hover out
      this.relationListeners.push(
        this.renderer.listen(button, "mouseleave", () => { this.hideTooltip(); })
      );
    });

    // console.log("Bind relation buttons completed.");
  }

  parseReferences(html: string) {
    this.listReferences = [];

    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    const items = doc.querySelectorAll('.tooltip-list li a');

    items.forEach(a => {
      const id = a.getAttribute('term-id') || null;
      const id_document = a.getAttribute('doc-id') || null;

      this.listReferences.push({
        id: id || null,
        id_document: id_document || null,
        title: a.innerHTML.trim(),
        raw_content: null
      });
    });

    if (this.listReferences.length > 1) {
      this.activePanelModalIds = [];
    } else if (this.listReferences.length === 1) {
      this.getLawClauseContent(
        this.listReferences[0].id,
        this.listReferences[0].id_document
      );
      this.activePanelModalIds = ['panel-0'];
    }
  }

  parseRelation(html: string, targetTooltipId: string = null) {
    this.listRelations = [];

    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');

    const items = doc.querySelectorAll('.tooltip-list li a');

    items.forEach(a => {
      const id = a.getAttribute('term-id') || null;
      const id_document = a.getAttribute('doc-id') || null;

      this.listRelations.push({
        id: id,
        id_document: id_document,
        title: a.innerHTML.trim(),
        right_raw_content: null,
        left_raw_content: null
      });
    });

    if (this.listRelations.length > 1) {
      this.activePanelModalIds = [];
    } else if (this.listRelations.length === 1) {
      this.getLawClauseContentFlexible(
        this.listRelations[0].id,
        this.listRelations[0].id_document,
        targetTooltipId
      );
      this.activePanelModalIds = ['panel-rel-0'];
    }
  }

  transform(value: string): string {
    return value.replace(/<[^>]+>/g, ""); // Xóa tất cả thẻ HTML
  }

  saveFileToListDocument() {
    const workspaceId = this.route.snapshot.params.id;
    this.isSavingToListDocument = true;

    // Lấy source gốc từ dataFile đang hiển thị
    this.fileInfor = this.dataFile;
    if (this.fileInfor.ID) {
      this.fileInfor.id = this.fileInfor.ID;
      delete this.fileInfor.ID;
    }

    this.fileInfor.workspace_id = workspaceId; // Thêm workspace_id vào fileInfor
    if (typeof this.fileInfor.id === "string") {
      this.fileInfor.id = this.fileInfor.doc_id;
    }

    this.viewDetailFile
      .addFileBySearch([this.fileInfor])
      .pipe(
        finalize(() => {
          this.isSavingToListDocument = false;
        })
      )
      .subscribe(
        (response: any) => {

          // Case 1: Xử lý Cảnh báo (Ví dụ: Đã lưu 0 văn bản...)
          if (response && response.status === 'warning') {
            this.toast.warning(
              response.message,
              response.status_title || "Cảnh báo",
              {
                closeButton: true,
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                enableHtml: true,
              }
            );
            // Vẫn reload lại list phòng trường hợp lưu 1 file thành công, 1 file trùng
            setTimeout(() => {
              this.workspace.isSaveFileFromSearch.next(true);
            }, 1000);
            return;
          }

          // Case 2: Xử lý Lỗi nghiệp vụ (Nếu BE trả 200 OK nhưng nội dung là lỗi)
          if (response && response.status === 'error') {
            this.toast.error(
              response.message,
              "Lỗi",
              {
                closeButton: true,
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                enableHtml: true,
              }
            );
            return;
          }

          this.toast.success(
            response.message || "Lưu tài liệu vào lịch sử tìm kiếm",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              enableHtml: true,
            }
          );

          setTimeout(() => {
            // Bắn tín hiệu cho ListDocumentComponent load lại danh sách
            this.workspace.isSaveFileFromSearch.next(true);
          }, 1000);

        },
        (error) => {
          this.toast.error("Không thể kết nối đến máy chủ", "Thất bại", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          // console.error("Lỗi khi lưu:", error);
        }
      );
  }

  extractClausesFromHtml(content: string): any[] {
    if (!content) return [];

    const clauses = [];

    // --- Xử lý nếu là MARKDOWN---
    if (content.includes('#')) {
      const lines = content.split('\n');
      let currentClause = null;
      let clauseIndex = 0;

      lines.forEach((line) => {
        const trimmedLine = line.trim();
        // Regex bắt các dòng bắt đầu bằng #, ##, ###... hoặc **Điều...**
        // Ví dụ: "## 1. Giới thiệu" hoặc "**Điều 1.**"
        const isHeader = /^#{1,6}\s+/.test(trimmedLine) ||
          (/^\*\*(Điều|Mục|Phần|Chương)\s+/.test(trimmedLine));

        if (isHeader) {
          if (currentClause) {
            clauses.push(currentClause);
          }
          clauseIndex++;
          // Loại bỏ ký tự markdown (#, *) để lấy title sạch
          const cleanTitle = trimmedLine.replace(/^[#*]+\s*/, '').replace(/[*]+$/, '').trim();

          currentClause = {
            title: cleanTitle,
            content: line + '\n', // Bắt đầu nội dung
            position: cleanTitle,
            order: clauseIndex
          };
        } else {
          if (currentClause) {
            currentClause.content += line + '\n';
          } else {
            // Nội dung trước header đầu tiên (Lời nói đầu...)
            // Có thể bỏ qua hoặc tạo một mục mở đầu nếu cần
          }
        }
      });

      if (currentClause) {
        clauses.push(currentClause);
      }
    }

    if (clauses.length === 0) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(content, 'text/html');
      const headers = doc.querySelectorAll('h1, h2, h3, h4, h5, h6, b, strong');

      let htmlClause = null;
      let htmlIndex = 0;

      headers.forEach((el) => {
        const text = (el.textContent || '').trim();
        // Logic nhận diện tiêu đề HTML
        if (text.length > 3 && text.length < 200) {
          const isLikelyHeader = /^(Điều|Khoản|Mục|Phần|Chương|\d+\.)/i.test(text) || el.tagName.startsWith('H');

          if (isLikelyHeader) {
            if (htmlClause) clauses.push(htmlClause);

            htmlIndex++;
            htmlClause = {
              title: text,
              content: el.outerHTML,
              position: text,
              order: htmlIndex
            };
          }
        }
      });
      if (htmlClause) clauses.push(htmlClause);
    }

    // --- Fallback cuối cùng: Nếu không tách được gì, lưu toàn bộ là 1 mục ---
    if (clauses.length === 0 && content.trim().length > 0) {
      clauses.push({
        title: "Nội dung tổng quan", // Hoặc lấy tên document
        content: content,
        position: "Toàn văn",
        order: 1
      });
    }

    return clauses;
  }

  scrollToTopContainer() {
    setTimeout(() => {
      const container = this.contentDiv?.nativeElement as HTMLElement;
      if (container) {
        container.scrollTop = 0; // ← Đẩy thanh cuộn về vị trí top
      }
    }, 0);
  }

  // scrollToClause(clauseId: string) {
  //   setTimeout(() => {
  //     if (!this.contentDiv?.nativeElement || !clauseId) {
  //       return;
  //     }

  //     const container = this.contentDiv.nativeElement as HTMLElement;
  //     let target: HTMLElement | null = null;

  //     // 1) Thử như luồng cũ: tìm theo id
  //     try {
  //       const escapedId =
  //         (window as any).CSS && (window as any).CSS.escape
  //           ? (window as any).CSS.escape(clauseId)
  //           : clauseId.replace(/"/g, '\\"');

  //       target = container.querySelector(
  //         `#${escapedId}`
  //       ) as HTMLElement | null;
  //     } catch (e) {
  //       console.warn("Selector không hợp lệ cho clauseId:", clauseId, e);
  //       target = null;
  //     }

  //     // 2) Nếu clauseId là số → coi là index heading (1-based)
  //     if (!target) {
  //       const num = Number(clauseId);
  //       if (Number.isInteger(num) && num > 0) {
  //         const headings = container.querySelectorAll<HTMLElement>(
  //           "h1,h2,h3,h4,h5,h6"
  //         );
  //         const idx = num - 1;
  //         if (headings[idx]) {
  //           target = headings[idx];
  //         }
  //       }
  //     }

  //     // 3) Không tìm được theo id/index → coi clauseId là TEXT của mục bên trái
  //     if (!target) {
  //       target = this.findSectionByTitle(container, clauseId);
  //     }

  //     if (target) {
  //       target.scrollIntoView({ behavior: "smooth", block: "start" });
  //       requestAnimationFrame(() => {
  //         if (this.avtiveTab === "toanvandanchieu" && !this.isInsideWorkspace()) {
  //           this.bindReferenceLinks();
  //           this.bindRelationButtons();
  //         }
  //       });
  //       target.classList.add("highlight-scroll");
  //       setTimeout(() => {
  //         target.classList.remove("highlight-scroll");
  //       }, 3000);
  //     } else {
  //       console.warn("Không tìm thấy target cho clauseId:", clauseId);
  //     }
  //   }, 0);
  // }

  // Thay thế hàm scrollToClause hiện tại bằng hàm này
  scrollToClause(clauseId: string) {
    setTimeout(() => {
      if (!this.contentDiv?.nativeElement) return;

      const container = this.contentDiv.nativeElement as HTMLElement;
      const target = container.querySelector(`#${clauseId}`) as HTMLElement;


      // --- BƯỚC 3: Scroll ---
      if (target) {
        // block: 'center' hoặc 'start' tùy trải nghiệm bạn muốn
        target.scrollIntoView({ behavior: "smooth", block: "center" });

        target.classList.add("highlight-scroll");
        setTimeout(() => target.classList.remove("highlight-scroll"), 3000);
      } else {
        console.warn("Không tìm thấy clauseId trong DOM:", clauseId);
      }

    }, 0);
  }

  public handleResponse(res: any): void {
    const relationTypeMapping: { [key: string]: string } = {
      van_ban_can_cu: "Văn bản căn cứ",
      van_ban_bi_bai_bo: "Văn bản bị bãi bỏ",
      van_ban_bi_bai_bo_mot_phan: "Văn bản bị bãi bỏ một phần",
      van_ban_bi_dinh_chi: "Văn bản bị đình chỉ",
      van_ban_bi_dinh_chi_mot_phan: "Văn bản bị đình chỉ một phần",
      van_ban_bi_dinh_chinh: "Văn bản bị đính chính",
      van_ban_bi_huy_bo: "Văn bản bị huỷ bỏ",
      van_ban_bi_huy_bo_mot_phan: "Văn bản bị huỷ bỏ một phần",
      van_ban_bi_thay_the: "Văn bản bị thay thế",
      van_ban_bi_thay_the_mot_phan: "Văn bản bị thay thế một phần",
      van_ban_chua_xac_dinh: "Văn bản chưa xác định",
      van_ban_chua_xac_dinh_mot_phan: "Văn bản chưa xác định một phần",
      van_ban_dan_chieu: "Văn bản dẫn chiếu",
      van_ban_duoc_huong_dan: "Văn bản được hướng dẫn",
      van_ban_quy_dinh_chi_tiet: "Văn bản quy định chi tiết",
      van_ban_duoc_sua_doi: "Văn bản được sửa đổi",
      van_ban_duoc_sua_doi_bo_sung: "Văn bản được sửa đổi bổ sung",
      van_ban_bai_bo: "Văn bản bãi bỏ",
      van_ban_bi_het_hieu_luc: "Văn bản bị hết hiệu lực",
      van_ban_bi_het_hieu_luc_mot_phan: "Văn bản bị hết hiệu lực một phần",
      van_ban_dinh_chi: "Văn bản đình chỉ",
      van_ban_dinh_chi_mot_phan: "Văn bản đình chỉ một phần",
      van_ban_duoc_quy_dinh_chi_tiet: "Văn bản được quy định chi tiết",
      van_ban_huong_dan: "Văn bản hướng dẫn",
      van_ban_het_hieu_luc: "Văn bản hết hiệu lực",
      van_ban_het_hieu_luc_mot_phan: "Văn bản hết hiệu lực một phần",
      van_ban_huy_bo: "Văn bản huỷ bỏ",
      van_ban_huy_bo_mot_phan: "Văn bản huỷ bỏ một phần",
      van_ban_sua_doi: "Văn bản sửa đổi",
      van_ban_sua_doi_bo_sung: "Văn bản sửa đổi bổ sung",
      van_ban_thay_the: "Văn bản thay thế",
      van_ban_thay_the_mot_phan: "Văn bản thay thế một phần",
      van_ban_hop_nhat: "Văn bản hợp nhất",
    };

    this.listPanels = Object.keys(relationTypeMapping)
      .filter((key) => Array.isArray(res[key]) && res[key].length > 0)
      .map((key) => ({
        title: relationTypeMapping[key],
        list: res[key],
      }));
    this.listPanelsFiltered = this.listPanels;

    this.activePanelIds = this.listPanels.map((_, idx) => "ngb-panel-" + idx);
  }

  getDetailFile(showLoading: boolean = false) {
    this.isFetchingContent = showLoading;

    this.viewDetailFile
      .getDetailFile(this.fileId, this.typeDocument)
      .pipe(finalize(() => (this.isFetchingContent = false)))
      .subscribe({
        next: (res) => {
          this.dataFile = res;
          this.safeHtml = this.sanitizer.bypassSecurityTrustHtml(
            `${this.dataFile?.toan_van || ""}`
          );

          this.scrollToTopContainer();
          if (this.clauseId) {
            setTimeout(() => {
              this.scrollToClause(this.clauseId);
            }, 0);
          }
        },
        error: (err) => {
          console.error("Lỗi khi lấy chi tiết file:", err);
        },
      });
  }

  getDetailFileForReference(fileId: string, typeDocument: string, typeCall: string = "ref") {
    this.isFetchingContentModal = true;
    if (typeCall === "rel") {
      this.relRefToDifferentDocument = true;
    } else {
      this.refToDifferentDocument = true;
    }
    this.referenceContentForModal = null;
    this.viewDetailFile
      .getDetailFile(fileId, typeDocument)
      .pipe(
        finalize(() => {
          this.isFetchingContentModal = false;
        })
      )
      .subscribe((res) => {
        this.dataReference = res;
        this.referenceContentForModal = this.sanitizer.bypassSecurityTrustHtml(
          `${this.dataReference?.toan_van || "Không tìm thấy nội dung dẫn chiếu"
          }` // để tránh angular tự xoá các id trong khi innerHTML đi vì lý do bảo mật
        );
        // this.scrollToTopContainer();
        // if (this.clauseId) {
        //   setTimeout(() => {
        //     this.scrollToClause(this.clauseId);
        //   }, 0); // Delay nhỏ để đảm bảo DOM cập nhật xong
        // }
      });
  }

  // getDetailFileFromSearch(showLoading: boolean = false) {
  //   this.isFetchingContent = showLoading;
  //   this.viewDetailFile
  //     .getDetailFileFromSearch(
  //       this.fileId,
  //       this.typeDocument,
  //       this.luocdo == "es" ? "1" : "0"
  //     )
  //     .pipe(
  //       finalize(() => {
  //         this.isFetchingContent = false;
  //       })
  //     )
  //     .subscribe({
  //       next: (res) => {
  //         this.dataFile = res;
  //         const isChatbotLike = this.detectChatbotLikeDoc(res);
  //         this.isChatbotDraftView = isChatbotLike;

  //         this.safeHtml = this.sanitizer.bypassSecurityTrustHtml(
  //           this.dataFile?.toan_van || ""
  //         );

  //         const htmlRef = this.dataFile?.html_with_reference || "";
  //         const cleanHtml = htmlRef.replace(/onclick="[^"]*"/g, "");
  //         this.safeHtmlWithReference =
  //           this.sanitizer.bypassSecurityTrustHtml(cleanHtml);
  //         // Nếu clauseId chưa có khi lần đầu gọi → lắng nghe để gọi lại khi có giá trị
  //         if (!this.clauseId) {
  //           const sub = this.viewDetailFile.clauseId
  //             .pipe(take(1)) // chỉ lắng nghe 1 lần
  //             .subscribe((newClauseId) => {
  //               if (newClauseId) {
  //                 this.clauseId = newClauseId;
  //                 // console.log("clauseId updated, recall getDetailFileFromSearch()");
  //                 this.getDetailFileFromSearch(true); // gọi lại hàm này
  //               }
  //               sub.unsubscribe();
  //             });
  //           return; // dừng hàm tại đây để chờ lần gọi lại
  //         }

  //         // Nếu clauseId đã có rồi → thực hiện scroll như bình thường
  //         setTimeout(() => {
  //           this.scrollToClause(this.clauseId);
  //         }, 0);
  //       },
  //       error: (err) => {
  //         console.error("Lỗi khi lấy chi tiết file từ tìm kiếm:", err);
  //       },
  //     });
  // }

  cleanHtmlContent(content: string): string {
    return content.replace(/onclick="[^"]*"/g, "");
  }

  getDetailFileFromSearch(showLoading: boolean = false) {
    this.isFetchingContent = showLoading;
    this.viewDetailFile
      .getDetailFileFromSearch(
        this.fileId,
        this.typeDocument,
        this.luocdo == "es" ? "1" : "0"
      )
      .pipe(
        finalize(() => {
          this.isFetchingContent = false;
        })
      )
      .subscribe({
        next: (res) => {
          this.dataFile = res;

          // 1. Bypass bảo mật để hiển thị HTML từ backend
          this.safeHtml = this.sanitizer.bypassSecurityTrustHtml(
            this.dataFile?.toan_van || ""
          );

          // 2. Ép buộc tắt chế độ Chatbot Draft (Markdown) để dùng chế độ hiển thị HTML thường
          this.isChatbotDraftView = false;
          // --------------------

          const htmlRef = this.dataFile?.html_with_reference || "";
          const cleanHtml = htmlRef.replace(/onclick="[^"]*"/g, "");
          this.safeHtmlWithReference =
            this.sanitizer.bypassSecurityTrustHtml(cleanHtml);

          if (this.typeDocument === 'searching') {
            this.viewDetailFile.getLawClausesSearch(this.fileId).subscribe({
              next: (clauses) => {

                this.dataFile.children = clauses;
                this.dataFile.terms = clauses;

                this.documentService.FileSearchTemp.next(this.dataFile);
              },
              error: (e) => {
                console.error("Không tải được danh sách điều khoản:", e);
                this.documentService.FileSearchTemp.next(this.dataFile);
              }
            });
          } else {
            // Với các loại khác, cập nhật sidebar ngay
            this.documentService.FileSearchTemp.next(this.dataFile);
          }

          if (!this.clauseId) {
            const sub = this.viewDetailFile.clauseId
              .pipe(take(1))
              .subscribe((newClauseId) => {
                if (newClauseId) {
                  this.clauseId = newClauseId;
                  this.getDetailFileFromSearch(true);
                }
                // sub.unsubscribe();
              });
            return;
          }

          setTimeout(() => {
            this.scrollToClause(this.clauseId);
          }, 0);
        },
        error: (err) => {
          console.error("Lỗi khi lấy chi tiết file từ tìm kiếm:", err);
        },
      });
  }

  contentLoaded() {
    setTimeout(() => {
      window.dispatchEvent(new Event("resize"));
    }, 300);
    this.isLoading = false;
  }
  getLuocDo() {
    if (!this.fileId) return;

    if (this.fileId === this.currentLuocDoId) return;

    if (this.luocDoInFlight.has(this.fileId)) return;
    this.luocDoInFlight.add(this.fileId);

    this.viewDetailFile.getLuocDo(this.fileId).pipe(
      finalize(() => {
        this.luocDoInFlight.delete(this.fileId);
      })
    ).subscribe(
      (res) => {
        this.handleResponse(res);
        this.listVanBanLienQuan = res.van_ban_lien_quan_khac;
        this.listVanBanLienQuanFilter = res.van_ban_lien_quan_khac;
        this.currentLuocDoId = this.fileId;
      },
      (error) => {
        this.listPanels = null;
        this.listPanelsFiltered = [];
      }
    );
  }

  closeViewer(): void {
    if (this.modal) {
      this.modal.dismiss('close');
      return;
    }

    // Fallback: dùng luồng quay lại cũ
    this.goBackSearch();
  }

  shouldShowCloseButton(): boolean {
    return !!this.showCloseButton;
  }

  changeNav(event) {
    const nextTab = event.nextId;
    const prevTab = this.avtiveTab || "toanvan"; // tab hiện tại TRƯỚC khi đổi

    // 🌟 Lưu lại lịch sử tab trước khi đổi
    if (!this.isRestoringTabFromHistory && nextTab !== prevTab) {
      const currentParams = {
        ...this.route.snapshot.queryParams,
        fileId: this.fileId,      // đảm bảo có fileId trong history
        tabs: prevTab,            // tab đang xem trước khi chuyển
      };
      this.navHistory.push(currentParams);
    }

    this.showPopover = false;
    this.avtiveTab = nextTab;
    this.hideTooltip();

    if (nextTab === "luocdo") {
      // this.getLuocDo();
      this.isToggleAddClause = false;
      this.listPanelsFiltered = this.listPanels;
    }

    if (nextTab === "lienquannoidung") {
      this.isToggleAddClause = false;
      this.listVanBanLienQuanFilter = this.listVanBanLienQuan;
    }

    if (nextTab === "vanbangoc") {
      this.isLoading = true;
    }

    if (nextTab === "tomtatvanban") {
      this.streamingDone = false;
      this.summarizeDocument();
    }

    if (nextTab === "toanvandanchieu") {
      setTimeout(() => {
        this.bindReferenceLinks();
        this.bindRelationButtons();
      }, 0);
    }

    if (nextTab === "dothi") {
      this.loadGraph();
    }
  }

  summarizeDocument() {
    this.isStreaming = true;
    this.viewDetailFile.summarizeDocument(this.fileId).subscribe(
      (res) => {
        this.isStreaming = false;
        if (res.results.length != 0) {
          this.statusSummarize = res.results[0].status;
          this.dataSummarize = res.results[0].summarize;
          this.statusStreaming = res.results[0].status;
        } else {
          this.dataSummarize = "Không có nội dung tóm tắt";
        }
      },
      (error) => {
        this.isStreaming = false;
        this.dataSummarize = "Không có dữ liệu";
        this.toast.error("Tóm tắt văn bản", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }
  async streamSumarize() {
    this.dataSummarize = "";
    this.isStreaming = true;
    try {
      const response = await fetch(
        `${environment.apichatbot}/stream_summarize/${this.fileId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      if (!response.body) {
        console.error("❌ No response body");
        return;
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";

      // this.checkDoneAnswer = false;
      while (true) {
        const { value, done } = await reader.read();
        if (done) {
          this.streamingDone = true;
          // console.log("streaming done");
          this.toast.info("Quá trình tóm tắt đã xong", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.isStreaming = false;
          this.saveSummarize(this.dataSummarize);
          break;
        }
        buffer += decoder.decode(value, { stream: true });

        // // console.log("json", buffer);
        const [jsonObjects, remaining] = this.extractJsonObjects(buffer);
        buffer = remaining;

        for (const obj of jsonObjects) {
          // console.log("obj", obj);
          if (obj.status_code == 400) {
            this.dataSummarize = "Không có nội dung tóm tắt";
          } else {
            if (obj.text) {
              this.dataSummarize += obj.text;
            }
            if (obj.answer) {
              this.dataSummarize = obj.answer;
            }
          }
        }
      }
    } catch (error) {
      this.toast.error("Thất bại", "", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
    }
  }
  extractJsonObjects(str: string): any[] {
    const objects: any[] = [];
    let depth = 0;
    let start = -1;
    let inString = false;
    let escape = false;

    for (let i = 0; i < str.length; i++) {
      const char = str[i];

      if (char === '"' && !escape) {
        inString = !inString;
      }

      if (!inString) {
        if (char === "{") {
          if (depth === 0) start = i;
          depth++;
        } else if (char === "}") {
          depth--;
          if (depth === 0 && start !== -1) {
            const jsonStr = str.slice(start, i + 1);
            try {
              objects.push(JSON.parse(jsonStr));
            } catch (e) {
              console.warn("⚠️ JSON parse failed", jsonStr);
            }
            start = -1;
          }
        }
      }

      escape = char === "\\" && !escape;
    }

    const remaining = depth > 0 && start !== -1 ? str.slice(start) : "";
    return [objects, remaining];
  }
  saveSummarize(dataSummarize) {
    const body = {
      document: this.fileId,
      summarize: dataSummarize,
    };
    this.viewDetailFile.saveSumarizeDocument(body).subscribe(
      (res) => {
        this.dataSummarize = dataSummarize;
        this.toast.success("Lưu tóm tắt văn bản", "Thành công", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      },
      (error) => {
        this.toast.error("Lưu tóm tắt văn bản", "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }
  saveSummarizeEdited() {
    const body = {
      document: this.fileId,
      summarize: this.valueSumarizeEdited,
    };
    this.viewDetailFile.saveSumarizeDocument(body).subscribe(
      (res) => {
        this.toast.success("Lưu chỉnh sửa tóm tắt văn bản", "Thành công", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        this.isEditSumarize = false;
      },
      (error) => {
        // this.toast.error("Lưu tóm tắt văn bản", "Thất bại", {
        //   closeButton: true,
        //   positionClass: "toast-top-right",
        //   toastClass: "toast ngx-toastr",
        // });
      }
    );
  }
  cancelSummarizeEdited() {
    this.isEditSumarize = false;
  }
  goBackSearch() {
    // báo cho list-document pop term gần nhất
    this.documentService.FileSearchTemp.next("Xoá");

    // 👉 Nếu còn history → back trong viewer trước
    if (this.navHistory.length > 0) {
      const prev = this.navHistory.pop();
      const prevTabs = prev.tabs || "toanvan";
      const prevFileId = prev.fileId || null;

      // 1) Cùng file, khác tab → back TAB
      if (prevFileId && prevFileId === this.fileId && prevTabs !== this.avtiveTab) {
        this.isRestoringTabFromHistory = true;

        this.avtiveTab = prevTabs;
        // Gọi lại changeNav để chạy đúng logic (tóm tắt, đồ thị, dẫn chiếu...)
        this.changeNav({ nextId: prevTabs } as any);

        this.isRestoringTabFromHistory = false;
        return;
      }

      // 2) Khác file / khác ngữ cảnh → back FILE bằng router như cũ
      this.isNavigatingBack = true;
      this.router.navigate([], {
        relativeTo: this.route,
        queryParams: prev,
        queryParamsHandling: "merge",
      });
      return;
    }

    // 👉 Không còn history nội bộ → thoát về list/search như luồng cũ
    this.documentService.goBack();
  }

  addVanBanCanCu() {
    if (window.innerWidth < 1200) {
      this.modalService.open(this.addRelateDocumentModal, {
        centered: true,
        size: "xl",
        backdrop: "static",
        keyboard: false,
      });
    } else {
      this.isToggleAddClause = !this.isToggleAddClause;
      this.bosungVanBan.typeSearch.next(false);
      this.bosungVanBan.typeAddFile.next("Văn bản căn cứ");
      this.documentService.rightSideBarValue.next(
        this.isToggleAddClause ? ShowSideBar.AddClause : ShowSideBar.Note
      );
    }
  }
  addVanBanLienQuan() {
    if (window.innerWidth < 1200) {
      this.modalService.open(this.addRelateDocumentModal, {
        centered: true,
        size: "xl",
        backdrop: "static",
        keyboard: false,
        scrollable: true,
      });
    } else {
      this.isToggleAddClause = !this.isToggleAddClause;
      this.bosungVanBan.typeSearch.next(false);
      this.bosungVanBan.typeAddFile.next("Văn bản liên quan");
      this.documentService.rightSideBarValue.next(
        this.isToggleAddClause ? ShowSideBar.AddClause : ShowSideBar.Note
      );
    }
  }
  viewFileLienQuan(fileLienQuan) {
    // this.viewDetailFile.fileName.next(fileLienQuan.trich_yeu);
    this.documentService.setBehavior(ShowContent.Document);
    this.documentService.FileSearchTemp.next(fileLienQuan);
    this.viewDetailFile.clauseId2.next(null); // tránh khi bấm vào lược đồ lại bị cache scroll vào điều trước đó
    // this.viewDetailFile.fileInfor.next(fileLienQuan);

    this.router.navigate([], {
      queryParams: {
        fileId: fileLienQuan.id || fileLienQuan.ID,
        es_id: fileLienQuan.ID,
        tabs: "toanvan",
        type: "search",
        luocdo: "es",
        fileName: fileLienQuan.trich_yeu,
        time: new Date().getTime(),
        save: true,
      },
      queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
    });
  }
  editTongQuan(soHieuRef, coQuanBanHanhRef, phamViRef) {
    this.isEditTongQuan = !this.isEditTongQuan;
    if (!this.isEditTongQuan) {
      // Lưu dữ liệu vào this.dataFile
      this.dataFile.so_hieu = soHieuRef.innerText.trim();
      this.dataFile.co_quan_ban_hanh = coQuanBanHanhRef.innerText.trim();
      this.dataFile.pham_vi = phamViRef.innerText.trim();

      // Chuẩn bị dữ liệu gửi đi
      const formData = new FormData();
      formData.append("so_hieu", this.dataFile.so_hieu);
      formData.append("loai_van_ban", this.dataFile.loai_van_ban);
      formData.append("co_quan_ban_hanh", this.dataFile.co_quan_ban_hanh);
      formData.append("pham_vi", this.dataFile.pham_vi);

      if (this.dataFile.ngay_ban_hanh)
        formData.append("ngay_ban_hanh", this.dataFile.ngay_ban_hanh);
      if (this.dataFile.ngay_co_hieu_luc)
        formData.append("ngay_co_hieu_luc", this.dataFile.ngay_co_hieu_luc);
      if (this.dataFile.ngay_dang_cong_bao)
        formData.append("ngay_dang_cong_bao", this.dataFile.ngay_dang_cong_bao);

      // Gửi API cập nhật
      this.viewDetailFile.editTongQuan(this.dataFile.id, formData).subscribe(
        (res) => {
          this.isEditTongQuan = false;
          this.toast.success("Cập nhật tổng quan", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        },
        (error) => {
          this.isEditTongQuan = true;
          this.toast.error("Cập nhật tổng quan", "Thất bại", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        }
      );
    } else {
      this.customDateOptions1.defaultDate = this.dataFile.ngay_ban_hanh;
      this.customDateOptions2.defaultDate = this.dataFile.ngay_co_hieu_luc;
      this.customDateOptions3.defaultDate = this.dataFile.ngay_dang_cong_bao;
      // Khi chuyển sang chế độ chỉnh sửa, focus vào input đầu tiên
      setTimeout(() => {
        this.soHieuRef.nativeElement.focus();
      });
    }
  }
  changeLoaiVanBan(event) {
    this.dataFile.loai_van_ban = event.value;
  }
  selectNgayBanHanh(event) {
    const date = event.target.value;
    this.dataFile.ngay_ban_hanh = date;
  }
  selectNgayCoHieuLuc(event) {
    const date = event.target.value;
    this.dataFile.ngay_co_hieu_luc = date;
  }
  selectNgayDangCongBao(event) {
    const date = event.target.value;
    this.dataFile.ngay_dang_cong_bao = date;
  }

  onActivate(event) {
    if (
      event.event.type === "click" &&
      event.column.name != "Hành động" &&
      event.column.name != "HÀNH ĐỘNG" &&
      event.column.name != "ACTION" &&
      event.column.name != "Action" &&
      event.column.name != "checkbox" &&
      event.column.name != ""
    ) {
      const fileLienQuan = event.row;
      this.documentService.setBehavior(ShowContent.Document);
      this.documentService.FileSearchTemp.next(fileLienQuan);
      this.viewDetailFile.clauseId2.next(null); // tránh khi bấm vào lược đồ lại bị cache scroll vào điều trước đó
      // this.viewDetailFile.fileInfor.next(fileLienQuan);

      this.router.navigate([], {
        queryParams: {
          fileId: fileLienQuan.id,
          // Dùng ID của văn bản lược đồ để gọi API Đồ thị
          es_id: fileLienQuan.ID,
          tabs: "toanvan",
          type: "search",
          time: new Date().getTime(),
          fileName: fileLienQuan.trich_yeu,
          save: true,
        },
        queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
      });
    }
  }
  deleteVanBanLienQuan(file) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this.viewDetailFile.deleteRelateFile(file.id).subscribe((res) => {
          this.toast.success("Đã xoá văn bản liên quan", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.getLuocDo();
        });
      }
    });
  }

  filterLuocDO(event) {
    const searchValue = event.target.value
      .toLowerCase()
      .trim()
      .replace(/(\s)-|-(\s)/g, "$1$2") // xóa dấu '-' khi đứng cạnh khoảng trắng
      .replace(/\s+/g, " ");
    this.listPanelsFiltered = this.listPanels
      .map((panel) => {
        const filteredList = panel.list.filter((item) =>
          item.title?.toLowerCase().includes(searchValue)
        );

        return {
          ...panel,
          list: filteredList,
        };
      })
      .filter((panel) => panel.list.length > 0); // chỉ giữ lại các panel có list khớp
  }
  filterVanBanLienQuan(event) {
    const searchValue = event.target.value
      .toLowerCase()
      .trim()
      .replace(/(\s)-|-(\s)/g, "$1$2") // xóa dấu '-' khi đứng cạnh khoảng trắng
      .replace(/\s+/g, " ");
    // console.log("searchValue", searchValue);

    this.listVanBanLienQuanFilter = this.listVanBanLienQuan.filter((item) =>
      item.title?.toLowerCase().includes(searchValue)
    );
  }

  private boldAdded = false;
  ngAfterViewChecked() {
    if (!this.boldAdded && this.contentDiv?.nativeElement) {
      this.addBoldToDieuElements();
      this.boldAdded = true; // gắn cờ để không thêm lại nhiều lần
    }
    // Khi bật edit thì focus
    if (this.isEditSumarize === true && this.sumarizeContent) {
      this.sumarizeContent.nativeElement.focus();
    }
  }

  addBoldToDieuElements() {
    if (this.contentDiv?.nativeElement) {
      const elements =
        this.contentDiv.nativeElement.querySelectorAll('[id^="dieu_"]');
      elements.forEach((el: HTMLElement) => {
        if (/^dieu_\d+$/.test(el.id)) {
          el.classList.add("font-weight-bold");
        }
      });
    }
  }

  showPopover = false;
  popoverPosition = { top: 0, left: 0 };
  selectedText = "";

  onTextSelect() {
    const contentElement = this.contentDiv.nativeElement;
    const selection = window.getSelection();
    const text = selection?.toString();

    if (text && selection?.rangeCount) {
      const range = selection.getRangeAt(0);
      const commonAncestor = range.commonAncestorContainer;
      // console.log(contentElement.contains(commonAncestor));

      if (!contentElement.contains(commonAncestor)) {
        this.showPopover = false;
        return;
      }

      this.selectedText = text;
      // this.chatbotService.textBoiDen.next(text);

      const rects = range.getClientRects();
      if (rects.length === 0) {
        this.showPopover = false;
        return;
      }

      const lastRect = rects[rects.length - 1];
      const parentRect = contentElement.getBoundingClientRect();

      const popoverWidth = 200; // Giả sử chiều rộng popover
      const popoverHeight = 100; // Giả sử chiều cao popover
      const padding = 15;

      // === Tính vị trí ngang (left) như trước ===
      const spaceRight = parentRect.right - lastRect.right;
      const spaceLeft = lastRect.left - parentRect.left;

      let leftPosition;
      if (spaceRight >= popoverWidth + padding) {
        leftPosition = lastRect.right - parentRect.left + padding;
      } else if (spaceLeft >= popoverWidth + padding) {
        leftPosition = lastRect.left - parentRect.left - popoverWidth + 40;
      } else {
        leftPosition = Math.min(
          parentRect.width - popoverWidth - 10,
          lastRect.right - parentRect.left + padding
        );
      }

      // === Tính vị trí dọc (top hoặc lên trên nếu không đủ chỗ) ===
      const spaceBelow = parentRect.bottom - lastRect.bottom;
      const spaceAbove = lastRect.top - parentRect.top;

      let topPosition;
      if (spaceBelow >= popoverHeight + padding) {
        // đủ chỗ bên dưới
        topPosition = lastRect.bottom - parentRect.top + 5;
      } else if (spaceAbove >= popoverHeight + padding) {
        // đủ chỗ bên trên
        topPosition = lastRect.top - parentRect.top - popoverHeight - 5;
      } else {
        // không đủ chỗ cả trên lẫn dưới, ưu tiên bên dưới
        topPosition = Math.max(5, lastRect.bottom - parentRect.top + 5);
      }

      this.popoverPosition = {
        top: topPosition,
        left: leftPosition,
      };

      this.showPopover = true;
    } else {
      this.showPopover = false;
    }
  }

  copyText() {
    navigator.clipboard.writeText(this.selectedText);
    this.showPopover = false;
    this.toast.success("Sao chép", "Thành công", {
      closeButton: true,
      positionClass: "toast-top-right",
      toastClass: "toast ngx-toastr",
    });
  }
  askChatbot() {
    this.showPopover = false;
    this.workspace.isNotesCollapsed.next(false); // Mở sidebar ghi chú
    this.documentService.rightSideBarValue.next(ShowSideBar.Chatbot); // Mở sidebar ghi chú
    this.chatbotService.textFormVanBan.next(this.selectedText); // để gửi text được sao chép sang chatbot
    this.chatbotService.textBoiDen.next(null); // để bên chatbot k hiển thị text đã chọn
  }
  createNote() {
    this.showPopover = false;
    this.workspace.isNotesCollapsed.next(false); // Mở sidebar ghi chú
    this.documentService.rightSideBarValue.next(ShowSideBar.Note); // Mở sidebar ghi chú
    this.noteService.textFromVanBan.next(this.selectedText); // để gửi text được sao chép sang chatbot
  }
  onScrollContent() {
    this.showPopover = false;
    this.hideTooltip();
  }
  highlightText() {
    // Gợi ý xử lý highlight: lưu vị trí, sửa innerHTML bằng cách wrap text được chọn
    alert("Chức năng highlight đang được phát triển");
    this.showPopover = false;
  }
  getFileExtension(url: string): string {
    if (!url) return "";
    const cleanUrl = url.split("?")[0];

    return cleanUrl.split(".").pop()?.toLowerCase() || "";
  }
  editSumarize(event: Event) {
    const target = event.target as HTMLElement;
    this.valueSumarizeEdited = target.innerText.trim(); // hoặc innerHTML nếu muốn giữ format
  }

  handleChatbotContentClick(event: MouseEvent) {
    const target = event.target as HTMLElement;
    const anchor = target.closest('a');

    if (anchor) {
      // Fix lỗi Sanitizer: Xóa prefix 'unsafe:' nếu có
      let href = anchor.getAttribute('href') || '';
      if (!href) return;
      // Loại bỏ unsafe: để logic startsWith bên dưới hoạt động chính xác
      if (href.startsWith('unsafe:')) {
        href = href.replace('unsafe:', '');
      }

      let docId = '';
      let clauseId = null;
      let docType = '';

      if (href.startsWith('legal:')) {
        event.preventDefault();
        const rawValue = href.replace('legal:', '');

        // Xử lý cả 2 định dạng: legal:142881 hoặc legal:dieu_1#142881
        if (rawValue.includes('#')) {
          const parts = rawValue.split('#');
          clauseId = parts[0];
          docId = parts[1];
        } else {
          docId = rawValue;
        }

        // 2. Xử lý link USER / UPLOAD
        docType = 'searching';
      }
      else if (href.startsWith('user:') || href.startsWith('upload:')) {
        event.preventDefault();
        const rawValue = href.replace(/^(user|upload):/, '');
        docId = rawValue.split('#')[0];
        docType = 'upload';
      }

      if (docId && docType) {
        // Gọi hàm mở tài liệu, hàm này sẽ cập nhật Router queryParams 
        // và kích hoạt ngOnInit của ViewDetailFileComponent để load data mới
        this.openDocumentFromLink(docId, clauseId, anchor.innerText, docType);
      }
    }
  }

  private openDocumentFromLink(docId: string, termId: string, fileName: string, type: string) {
    // 1. Reset scroll cũ
    this.viewDetailFile.clauseId2.next(null);

    // 2. Điều hướng Router
    this.router.navigate([], {
      queryParams: {
        fileId: docId,
        tabs: "toanvan",
        type: type,
        luocdo: "es",
        fileName: fileName,
        time: new Date().getTime(),
        save: true,
        openMode: "chatbot",
      },
      queryParamsHandling: "merge",
    }).then(() => {
      if (termId) {
        setTimeout(() => {
          this.viewDetailFile.clauseId2.next(termId);
        }, 500);
      }
    });

    // 3. Báo cho Sidebar bên trái
    const fakeFile = {
      id: docId,
      es_id: docId,
      name: fileName,
      trich_yeu: fileName,
      types: type === 'searching' ? 99 : 0
    };

    this.documentService.FileSearchTemp.next(fakeFile);
    this.documentService.setBehavior(ShowContent.Search);
  }

  ngOnDestroy(): void {
    this._unSubscribe.next(true);
    this._unSubscribe.complete();
    this.viewDetailFile.clauseId.next(null);
    this.viewDetailFile.clauseId2.next(null);
    this._destroy$.next();
    this._destroy$.complete();
    // this.viewDetailFile.fileName.next("");
    this.hideTooltip();
  }

  cleanSummarize(text: string): string {
    if (!text) return "";

    return text
      .replace(/\r\n/g, "\n") // Chuẩn hoá xuống dòng
      .replace(/<\/think>\s*\n+\s*<answer>/g, "</think><answer>") // Gộp sát </think><answer>
      .replace(/\n{2,}/g, "\n") // Gộp nhiều \n thành 1
      .replace(/<\/?answer>/g, "") // Xóa thẻ <answer>
      .replace(/^\s*-\s+/gm, "\\- ") // Escape dấu gạch đầu dòng
      .replace(/^(\d+)\.\s+/gm, "$1\\. ") // Escape số thứ tự 1. 2. ...
      .trim();
  }

  private resetGraphFormState(): void {
    this.graphFormState = {
      search_legal_term: "VAN_BAN",
      isSearchAdvance: false,
      selectedBoLocMoiQuanHe: [],
      selectedTinhTrangHieuLuc: [],
      selectedBoLocLoaiVanBan: [],
      selectedCoQuanBanHanh: [],
      depth: 1,
      global_limit: 100,
      limit_per_seed: 100,
      dateFilterMode: null,
      dateFilterFrom: null,
      dateFilterTo: null,
      depthError: "",
      global_limitError: "",
      limit_per_seedError: "",
    };
  }

  /**
   * Get cached graph UI state for current document
   * Returns the actual cached objects (Set/Map) so child can modify them directly
   */
  getGraphUIState(): {
    hiddenNodeIds: Set<string>;
    previousNodePositions: Map<string, { x: number; y: number }>;
    documentListExpanded: boolean;
    showDocumentTable: boolean;
  } {
    const graphId =
      this.typeDocument === "searching"
        ? this.fileId
        : this.esId || this.fileId;

    if (!graphId) {
      return {
        hiddenNodeIds: new Set(),
        previousNodePositions: new Map(),
        documentListExpanded: false,
        showDocumentTable: false,
      };
    }

    let uiState = this.cachedGraphUIState.get(graphId);
    if (!uiState) {
      uiState = {
        hiddenNodeIds: new Set<string>(),
        previousNodePositions: new Map<string, { x: number; y: number }>(),
        documentListExpanded: false,
        showDocumentTable: false,
      };
      this.cachedGraphUIState.set(graphId, uiState);
    }
    return uiState;
  }

  /**
   * Update graph UI state from child component
   * Since Set/Map are passed by reference, the child modifies the cached objects directly.
   * This method is mainly for updating primitive values (booleans) and ensuring the cache is up to date.
   */
  onGraphUIStateChange(event: {
    hiddenNodeIds?: Set<string>;
    previousNodePositions?: Map<string, { x: number; y: number }>;
    documentListExpanded?: boolean;
    showDocumentTable?: boolean;
  }): void {
    const graphId =
      this.typeDocument === "searching"
        ? this.fileId
        : this.esId || this.fileId;

    if (!graphId) return;

    let uiState = this.cachedGraphUIState.get(graphId);
    if (!uiState) {
      uiState = {
        hiddenNodeIds: new Set<string>(),
        previousNodePositions: new Map<string, { x: number; y: number }>(),
        documentListExpanded: false,
        showDocumentTable: false,
      };
      this.cachedGraphUIState.set(graphId, uiState);
    }

    // For Set/Map, the child modifies them directly via reference, so we just update the reference if provided
    // This ensures the cache always has the latest reference (though usually it's the same object)
    if (event.hiddenNodeIds !== undefined) {
      uiState.hiddenNodeIds = event.hiddenNodeIds;
    }
    if (event.previousNodePositions !== undefined) {
      uiState.previousNodePositions = event.previousNodePositions;
    }
    // For primitives, update the cached values
    if (event.documentListExpanded !== undefined) {
      uiState.documentListExpanded = event.documentListExpanded;
    }
    if (event.showDocumentTable !== undefined) {
      uiState.showDocumentTable = event.showDocumentTable;
    }
  }

  resetReferenceModalState() {
    this.docChosen = null;
    this.refToDifferentDocument = false;
    this.listReferences = [];
  }

  handleShowReference(element: HTMLElement) {
    this.showPopover = false;
    this.resetReferenceModalState();
    this.resetRelationModalState();
    const docId = element.getAttribute("doc-id");
    const termId = element.getAttribute("term-id");
    const isSameDoc = docId === this.esId || docId === this.fileId;
    this.titleModalReference = !isSameDoc
      ? "Nội dung dẫn chiếu"
      : "Nội dung tham chiếu";

    if (isSameDoc && termId == "None") {
      this.getDetailFileForReference(docId, "search", "ref");
    }

    // Tham chiếu cùng tài liệu, có termId cụ thể
    else if (isSameDoc && termId && termId != "None") {
      this.getLawClauseContent(termId, docId);
    }

    // Dẫn chiếu khác tài liệu
    else if (!isSameDoc) {
      // Dẫn chiếu đến điều/khoản/... của văn bản khác
      if (termId && termId != "None") {
        this.docChosen = docId;
        this.getLawClauseContent(termId, docId);

        // Dẫn chiếu đến văn bản khác
      } else {
        this.getDetailFileForReference(docId, "search", "ref");
      }
    }

    this.modalService.open(this.referenceModal, {
      centered: true,
      size: "lg",
    });
  }

  resetRelationModalState() {
    this.relationTitleModal = null;
    this.relationType = null;
    this.storedTooltipId = null;
    this.relRefToDifferentDocument = false;
  }

  handleShowRelation(element: HTMLElement, content: string = null) {
    this.showPopover = false;
    this.resetRelationModalState();
    this.resetReferenceModalState();
    this.storedTooltipId = element.getAttribute("tooltip_id");
    this.relationTitleModal = this.getTooltipTitle(element.className, element.getAttribute("tooltip_id")) || null;
    this.parseRelation(content, element.getAttribute("tooltip_id"));
    this.relationType = element.getAttribute("data-action");

    this.modalService.open(this.relationModal, {
      centered: true,
      size: "lg",
    });
  }

  formatContentForTooltip(rawContent?: string): string {
    if (!rawContent) return null;

    return rawContent ? rawContent.replace(/<br\s*\/?>/gi, "\n").trim() : null;
  }

  getContentTooltip(targetClass: string, tooltipId: string): string | null {
    const tooltipContentEl = document.querySelector('div[class="' + targetClass + '"][tooltip_id="' + tooltipId + '"]');
    return tooltipContentEl ? tooltipContentEl.innerHTML.trim() : null;
  }

  getTooltipTitle(targetClass: string, tooltipId: string): string | null {
    const tooltipContentEl = document.querySelector('div[class="' + targetClass + '"][tooltip_id="' + tooltipId + '"]');
    const anchorsTitle = tooltipContentEl ? tooltipContentEl.querySelector('a[class="tooltip-title"]') : null;
    return anchorsTitle ? anchorsTitle.innerHTML.trim() || null : null;
  }

  formatDatetime(datetimeStr: string): string {
    if (!datetimeStr) return "";

    const date = new Date(datetimeStr);
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  }

  // Hiển thị tooltip tham chiếu/dẫn chiếu
  showTooltip(event: MouseEvent, link: HTMLElement) {
    let tooltipContent = this.formatContentForTooltip(
      this.getContentTooltip(link.className, link.getAttribute("tooltip_id")) || link.getAttribute("tieu_de") || null
    );
    const docId = link.getAttribute("doc-id");
    const termId = link.getAttribute("term-id");
    const isSameDoc = docId === this.esId || docId === this.fileId;

    if (!tooltipContent) {
      // const container = this.elRef.nativeElement.querySelector('.reference-container');
      const container = this.contentDiv?.nativeElement;

      // Cùng file, không có termId
      if (isSameDoc && termId === "None") {
        const loai1 = container.querySelector(
          'a[name="loai_1"]'
        ) as HTMLElement;
        const loai1Name = container.querySelector(
          'a[name="loai_1_name"]'
        ) as HTMLElement;

        if (loai1 && loai1Name) {
          tooltipContent = `${loai1.textContent?.trim() || ""} ${loai1Name.textContent?.trim() || ""
            }`;
        } else {
          tooltipContent = "Không tìm thấy nội dung dẫn chiếu";
        }
      }

      // Cùng file, có termId cụ thể
      else if (isSameDoc && termId && termId !== "None") {
        const termEl = container.querySelector(`#${termId}`) as HTMLElement;
        tooltipContent =
          termEl?.textContent?.trim() || "(Không tìm thấy nội dung dẫn chiếu)";
      }

      // Khác file
      else if (!isSameDoc) {
        tooltipContent = link.textContent?.trim() || "(Không có tên dẫn chiếu)";
      }

      // Nếu rỗng, không hiển thị tooltip
      if (!tooltipContent) return;
    }

    // Tạo tooltip container
    const tooltip = this.renderer.createElement("div");
    this.renderer.addClass(tooltip, "custom-tooltip");

    // Tạo phần nội dung chính
    const contentDiv = this.renderer.createElement("div");
    this.renderer.addClass(contentDiv, "tooltip-content");
    this.renderer.setProperty(contentDiv, "textContent", tooltipContent.trim());

    // Tạo phần gợi ý bên dưới
    const hintDiv = this.renderer.createElement("div");
    this.renderer.addClass(hintDiv, "tooltip-hint");

    // Tự tham chiếu chính nó thì không hiển thị gợi ý nữa
    this.renderer.setProperty(hintDiv, "textContent", "(Nhấn để xem chi tiết)");

    // Gắn 2 phần tử vào tooltip
    this.renderer.appendChild(tooltip, contentDiv);
    // if (!(isSameDoc && termId === "None")) {
    this.renderer.appendChild(tooltip, hintDiv);
    // }

    // Đặt tooltip trên trang
    this.renderer.setStyle(tooltip, "position", "fixed");
    this.renderer.setStyle(tooltip, "top", `${event.clientY + 10}px`);
    this.renderer.setStyle(tooltip, "left", `${event.clientX + 10}px`);
    this.renderer.setStyle(tooltip, "z-index", "9999");
    this.renderer.setStyle(tooltip, "pointer-events", "none");

    // this.elRef.nativeElement.appendChild(tooltip);
    // document.body.appendChild(tooltip);
    this.renderer.appendChild(document.body, tooltip);
    this.tooltipEl = tooltip;

    // console.log("Called showTooltip");
  }

  // Hiển thị tooltip xem thông tin chi tiết
  showTooltipDetail(id: string = null, id_document: string = null, pos: string = null, event: MouseEvent) {
    // if (!id || !id_document) return;
    let detailContent = null;
    let tooltipContent = '';

    if (!pos) {
      if (this.refToDifferentDocument) {
        tooltipContent += this.dataReference.so_hieu ? `<b>Số hiệu:</b> ${this.dataReference.so_hieu}<br>` : '';
        tooltipContent += this.dataReference.trich_yeu ? `<b>Trích yếu:</b> ${this.dataReference.trich_yeu}<br>` : '';
        tooltipContent += this.dataReference.tinh_trang_hieu_luc ? `<b>Tình trạng hiệu lực:</b> ${this.dataReference.tinh_trang_hieu_luc}<br>` : '';
        tooltipContent += this.dataReference.ngay_ban_hanh ? `<b>Ngày ban hành:</b> ${this.formatDatetime(this.dataReference.ngay_ban_hanh)}<br>` : '';
        tooltipContent += this.dataReference.ngay_co_hieu_luc ? `<b>Ngày có hiệu lực:</b> ${this.formatDatetime(this.dataReference.ngay_co_hieu_luc)}<br>` : '';
        tooltipContent += this.dataReference.co_quan_ban_hanh ? `<b>Cơ quan ban hành:</b> ${this.dataReference.co_quan_ban_hanh}<br>` : '';
      } else {
        detailContent = this.listReferences.find(item => item.id === id && item.id_document === id_document);
        if (!detailContent) {
          tooltipContent = 'Không tìm thấy thông tin';
        } else {
          tooltipContent += detailContent.so_hieu ? `<b>Số hiệu:</b> ${detailContent.so_hieu}<br>` : '';
          tooltipContent += detailContent.trich_yeu ? `<b>Trích yếu:</b> ${detailContent.trich_yeu}<br>` : '';
          tooltipContent += detailContent.tinh_trang_hieu_luc ? `<b>Tình trạng hiệu lực:</b> ${detailContent.tinh_trang_hieu_luc}<br>` : '';
          tooltipContent += detailContent.ngay_ban_hanh ? `<b>Ngày ban hành:</b> ${this.formatDatetime(detailContent.ngay_ban_hanh)}<br>` : '';
          tooltipContent += detailContent.ngay_co_hieu_luc ? `<b>Ngày có hiệu lực:</b> ${this.formatDatetime(detailContent.ngay_co_hieu_luc)}<br>` : '';
          tooltipContent += detailContent.co_quan_ban_hanh ? `<b>Cơ quan ban hành:</b> ${detailContent.co_quan_ban_hanh}<br>` : '';
        }
      }
    } else {
      detailContent = this.listRelations.find(item => item.id === id && item.id_document === id_document);
      if (!detailContent) {
        tooltipContent = 'Không tìm thấy thông tin';
      } else {
        if (pos === 'r') {
          tooltipContent += detailContent.r_so_hieu ? `<b>Số hiệu:</b> ${detailContent.r_so_hieu}<br>` : '';
          tooltipContent += detailContent.r_trich_yeu ? `<b>Trích yếu:</b> ${detailContent.r_trich_yeu}<br>` : '';
          tooltipContent += detailContent.r_tinh_trang_hieu_luc ? `<b>Tình trạng hiệu lực:</b> ${detailContent.r_tinh_trang_hieu_luc}<br>` : '';
          tooltipContent += detailContent.r_ngay_ban_hanh ? `<b>Ngày ban hành:</b> ${this.formatDatetime(detailContent.r_ngay_ban_hanh)}<br>` : '';
          tooltipContent += detailContent.r_ngay_co_hieu_luc ? `<b>Ngày có hiệu lực:</b> ${this.formatDatetime(detailContent.r_ngay_co_hieu_luc)}<br>` : '';
          tooltipContent += detailContent.r_co_quan_ban_hanh ? `<b>Cơ quan ban hành:</b> ${detailContent.r_co_quan_ban_hanh}<br>` : '';
        } else if (pos === 'l') {
          tooltipContent += detailContent.l_so_hieu ? `<b>Số hiệu:</b> ${detailContent.l_so_hieu}<br>` : '';
          tooltipContent += detailContent.l_trich_yeu ? `<b>Trích yếu:</b> ${detailContent.l_trich_yeu}<br>` : '';
          tooltipContent += detailContent.l_tinh_trang_hieu_luc ? `<b>Tình trạng hiệu lực:</b> ${detailContent.l_tinh_trang_hieu_luc}<br>` : '';
          tooltipContent += detailContent.l_ngay_ban_hanh ? `<b>Ngày ban hành:</b> ${this.formatDatetime(detailContent.l_ngay_ban_hanh)}<br>` : '';
          tooltipContent += detailContent.l_ngay_co_hieu_luc ? `<b>Ngày có hiệu lực:</b> ${this.formatDatetime(detailContent.l_ngay_co_hieu_luc)}<br>` : '';
          tooltipContent += detailContent.l_co_quan_ban_hanh ? `<b>Cơ quan ban hành:</b> ${detailContent.l_co_quan_ban_hanh}<br>` : '';
        }
      }
    }

    // Tạo tooltip container
    const tooltip = this.renderer.createElement("div");
    this.renderer.addClass(tooltip, "custom-tooltip-detail");

    // Tạo phần nội dung chính (là innerHTML để giữ định dạng)
    const contentDiv = this.renderer.createElement("div");
    this.renderer.addClass(contentDiv, "tooltip-content");
    this.renderer.setProperty(contentDiv, "innerHTML", tooltipContent.trim());

    // Gắn phần tử vào tooltip
    this.renderer.appendChild(tooltip, contentDiv);

    // Đặt tooltip trên trang
    this.renderer.setStyle(tooltip, "position", "fixed");
    this.renderer.setStyle(tooltip, "top", `${event.clientY + 10}px`);
    this.renderer.setStyle(tooltip, "left", `${event.clientX + 10}px`);
    this.renderer.setStyle(tooltip, "z-index", "9999");
    this.renderer.setStyle(tooltip, "pointer-events", "none");

    // this.elRef.nativeElement.appendChild(tooltip);
    // document.body.appendChild(tooltip);
    this.renderer.appendChild(document.body, tooltip);
    this.tooltipEl = tooltip;

    // console.log("Called showTooltipDetail");
  }

  showTooltipBtnRelation(event: MouseEvent, button: HTMLElement) {
    let tooltipContent = this.formatContentForTooltip(
      this.getContentTooltip(button.className, button.getAttribute("tooltip_id")) || button.getAttribute("tieu_de") || null
    );
    if (!tooltipContent) return;

    const dataAction = button.getAttribute("data-action");

    // Tạo tooltip container
    const tooltip = this.renderer.createElement("div");
    this.renderer.addClass(tooltip, "custom-tooltip-relation");

    // Tạo phần nội dung chính (là innerHTML để giữ định dạng)
    const contentDiv = this.renderer.createElement("div");
    this.renderer.addClass(contentDiv, "tooltip-content-relation");

    let contentToInnerHTML = tooltipContent.trim();
    if (dataAction == "Đính chính") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-dinh_chinh');
    } else if (dataAction == "Sửa đổi bổ sung") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-sua_doi_bo_sung');
    } else if (dataAction == "Sửa đổi") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-sua_doi');
    } else if (dataAction == "Bổ sung") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-bo_sung');
    } else if (dataAction == "Thay thế") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-thay_the');
    } else if (dataAction == "Hướng dẫn") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-huong_dan');
    } else if (dataAction == "Bãi bỏ") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-bai_bo');
    } else if (dataAction == "Bãi bỏ cụm từ") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-bai_bo_cum_tu');
    } else if (dataAction == "Quy định chi tiết") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-quy_dinh_chi_tiet');
    } else if (dataAction == "Dẫn chiếu") {
      contentToInnerHTML = contentToInnerHTML.replace('tooltip-title', 'tooltip-title text-color-dan_chieu');
    }

    this.renderer.setProperty(contentDiv, "innerHTML", contentToInnerHTML);

    // Gắn phần tử vào tooltip
    this.renderer.appendChild(tooltip, contentDiv);

    // Đặt tooltip trên trang
    this.renderer.setStyle(tooltip, "position", "fixed");
    this.renderer.setStyle(tooltip, "top", `${event.clientY + 10}px`);
    this.renderer.setStyle(tooltip, "left", `${event.clientX + 10}px`);
    this.renderer.setStyle(tooltip, "z-index", "9999");
    this.renderer.setStyle(tooltip, "pointer-events", "none");

    // this.elRef.nativeElement.appendChild(tooltip);
    // document.body.appendChild(tooltip);
    this.renderer.appendChild(document.body, tooltip);
    this.tooltipEl = tooltip;

    // console.log("Called showTooltipBtnRelation");
  }

  hideTooltip() {
    if (this.tooltipEl) {
      // this.renderer.removeChild(this.elRef.nativeElement, this.tooltipEl);
      // document.body.removeChild(this.tooltipEl);
      this.renderer.removeChild(document.body, this.tooltipEl);
      this.tooltipEl = null;

      // console.log("Called hideTooltip");
    }
  }

  private loadGraph(): void {
    // console.log("this.esId", this.esId)
    // console.log("this.fileId", this.fileId)

    const graphId =
      this.typeDocument === "searching"
        ? this.fileId
        : this.esId || this.fileId;

    // console.log("graphId",graphId)

    if (!graphId) return;

    // Check cache first - if we have cached data for this document, use it instead of making API call
    const cachedData = this.cachedGraphData.get(graphId);
    if (cachedData && this.cachedGraphDocumentId === graphId) {
      // Use cached data, no API call needed
      // This preserves graph state (expansions, hidden nodes, etc.) when switching tabs
      this.graphData = cachedData;
      this.viewDetailFile.graphData.next(cachedData);
      this.graphNotFound = false;
      this.isLoadingGraph = false;
      // UI state will be restored via @Input bindings when component is recreated
      return;
    }

    // No cache or document changed - make API call with default body
    const defaultBody = {
      co_quan_ban_hanh: [],
      loai_van_ban: [],
      node_ids: [String(graphId)],
      relationship_types: [],
      target_node_type: "VAN_BAN",
      tinh_trang_hieu_luc: [],
      ban_hanh_year_from: null,
      ban_hanh_year_to: null,
      hieu_luc_year_from: null,
      hieu_luc_year_to: null,
    };

    this.isLoadingGraph = true;
    this.graphNotFound = false;
    this.viewDetailFile.getDoThi(defaultBody).subscribe(
      (res) => {
        // // console.log('Graph data loaded:', res);
        this.graphData = res;
        // Update cache with fresh data
        this.cachedGraphData.set(graphId, res);
        this.cachedGraphDocumentId = graphId;
        this.viewDetailFile.graphData.next(res);
        this.graphNotFound = false;
        this.isLoadingGraph = false;
      },
      (error) => {
        // console.error('Error loading graph data:', error);
        this.graphData = null;
        this.viewDetailFile.graphData.next(null);
        if (error.error === "Không tìm thấy dữ liệu đồ thị") {
          this.graphNotFound = true;
        }
        this.isLoadingGraph = false;
      }
    );
  }

  removeRedundants(content: string) {
    const redundants = [
      "[TABLE]", "[\\TABLE]",
      "[IMAGE][\\IMAGE]",
    ];

    let cleanedContent = content;

    redundants.forEach((item) => {
      if (item.indexOf("\\") === -1) {
        const regex = new RegExp(this.escapeRegExp(item), "g");
        cleanedContent = cleanedContent.replace(regex, "");
      } else if (item.indexOf("\\") !== -1) {
        const parts = item.split("\\");
        const startTag = parts[0];
        const endTag = parts[1];
        const regex = new RegExp(this.escapeRegExp(startTag) + "[\\s\\S]*?" + this.escapeRegExp(endTag), "g");
        cleanedContent = cleanedContent.replace(regex, "");
      }
    });

    return cleanedContent;
  }

  escapeRegExp(string: string) {
    return string.replace(/[.*+?^=!:${}()|\[\]\/\\]/g, '\\$&'); // Escape các ký tự đặc biệt trong regex
  }

  formatContentForModal(rawContent: string): string {
    return rawContent
      ? rawContent.replace(/\n/g, "<br>")
      : "Không tìm thấy nội dung dẫn chiếu";
  }

  formatSafeHtml(rawContent: string) {
    const isHtml = /<\/?[a-z][\s\S]*>/i.test(rawContent);

    const contentToClean = isHtml
      ? rawContent
      : rawContent.replace(/\n/g, "<br>");

    const cleanedHtml = cleanWordHtml(contentToClean || "Không tìm thấy nội dung dẫn chiếu");
    return this.sanitizer.bypassSecurityTrustHtml(cleanedHtml);
  }

  containsHtml(raw: string): boolean {
    return /<\s*(table|tr|td|p|div|span|br)/i.test(raw);
  }

  replaceNewlineOutsideTags(text: string): string {
    return text.replace(/(>[^<]*)\n([^<]*<)/g, (m, before, after) => {
      return before.replace(/\n/g, "<br>") + after.replace(/\n/g, "<br>");
    }).replace(/\n/g, "<br>");
  }

  formatWordHtml(rawContent: string) {
    let processed = rawContent ?? "";

    if (this.containsHtml(processed)) {
      processed = this.replaceNewlineOutsideTags(processed);
    } else {
      processed = processed.replace(/\n/g, "<br>");
    }

    const cleaned = cleanWordHtml(processed);
    // return this.sanitizer.bypassSecurityTrustHtml(cleaned);
    return cleaned;
  }


  getLawClauseContent(clauseId: string, docId: string) {
    if (clauseId && docId) {
      this.listReferences = [];
      this.referenceContentForModal = null;
      this.isFetchingContentModal = true;
      this.viewDetailFile
        .getLawClauseContent(clauseId, docId)
        .pipe(
          finalize(() => {
            this.isFetchingContentModal = false;
          })
        )
        .subscribe({
          next: (res) => {
            // console.log("Nội dung điều khoản nhận được:", res);
            // this.referenceContentForModal = this.sanitizer.bypassSecurityTrustHtml(formattedContent);
            // Hiện tại chỉ về một điều nên sẽ add điều đó vào listReferences để hiển thị, sau nếu res trả nhiều điều thì sẽ xử lý khác
            this.listReferences.push(res);

            // Nếu listReferences chỉ có 1 phần tử thì mở panel luôn
            if (this.listReferences.length === 1) {
              this.activePanelModalIds = this.listReferences.map(
                (_, i) => `panel-${i}`
              );
            } else {
              this.activePanelModalIds = [];
            }
          },
          error: (err) => {
            console.error("Lỗi khi lấy nội dung điều khoản:", err);
          },
        });
    }
  }

  getLawClauseContentFlexible(clauseId: string, docId: string, targetTooltipId: string = this.storedTooltipId) {
    if (clauseId && docId) {
      this.listLoading.push({ clauseId, docId, rightContentLoading: true, leftContentLoading: true });
      // Lấy nội dung phải
      this.viewDetailFile
        .getLawClauseContent(clauseId, docId)
        .pipe(
          finalize(() => {
            const loadingItem = this.listLoading.find(
              (ref) => ref.clauseId === clauseId && ref.docId === docId
            );
            if (loadingItem) {
              loadingItem.rightContentLoading = false;
            }
            if (!loadingItem?.leftContentLoading && !loadingItem?.rightContentLoading) {
              this.listLoading = this.listLoading.filter(
                (ref) => !(ref.clauseId === clauseId && ref.docId === docId)
              );
            }
          })
        )
        .subscribe({
          next: (res) => {
            // console.log("Nội dung phải nhận được:", res);
            this.listRelations.forEach((relation) => {
              if (relation.id == clauseId && relation.id_document == docId) {
                relation.right_raw_content = this.removeRedundants(res.raw_content);
                relation.r_so_hieu = res.so_hieu;
                relation.r_trich_yeu = res.trich_yeu;
                relation.r_tinh_trang_hieu_luc = res.tinh_trang_hieu_luc;
                relation.r_ngay_ban_hanh = res.ngay_ban_hanh;
                relation.r_ngay_co_hieu_luc = res.ngay_co_hieu_luc;
                relation.r_co_quan_ban_hanh = res.co_quan_ban_hanh;
              }
            });
          },
          error: (err) => {
            console.error("Lỗi khi lấy nội dung điều khoản:", err);
          },
        }
        );
      // Lấy nội dung trái (gốc)
      const originalDocId = this.esId || this.fileId;
      const originalClauseId = targetTooltipId.split("-")[1]; // Lấy từ tooltip_id dạng ref-{clauseId}-{docId}
      this.viewDetailFile
        .getLawClauseContent(originalClauseId, originalDocId)
        .pipe(
          finalize(() => {
            const loadingItem = this.listLoading.find(
              (ref) => ref.clauseId === clauseId && ref.docId === docId
            );
            if (loadingItem) {
              loadingItem.leftContentLoading = false;
            }
            if (!loadingItem?.leftContentLoading && !loadingItem?.rightContentLoading) {
              this.listLoading = this.listLoading.filter(
                (ref) => !(ref.clauseId === clauseId && ref.docId === docId)
              );
            }
          })
        )
        .subscribe({
          next: (res) => {
            // console.log("Nội dung trái nhận được:", res);
            this.listRelations.forEach((relation) => {
              if (relation.id == clauseId && relation.id_document == docId) {
                relation.left_raw_content = this.removeRedundants(res.raw_content);
                relation.l_so_hieu = res.so_hieu;
                relation.l_trich_yeu = res.trich_yeu;
                relation.l_tinh_trang_hieu_luc = res.tinh_trang_hieu_luc;
                relation.l_ngay_ban_hanh = res.ngay_ban_hanh;
                relation.l_ngay_co_hieu_luc = res.ngay_co_hieu_luc;
                relation.l_co_quan_ban_hanh = res.co_quan_ban_hanh;
              }
            });
          },
          error: (err) => {
            console.error("Lỗi khi lấy nội dung điều khoản:", err);
          },
        }
        );
    }
  }

  onPanelChange(event: NgbPanelChangeEvent) {
    const id = event.panelId;
    if (this.activePanelModalIds.includes(id)) {
      // Nếu đang mở → đóng
      this.activePanelModalIds = this.activePanelModalIds.filter(
        (p) => p !== id
      );
    } else {
      // Nếu đang đóng → mở thêm
      this.activePanelModalIds = [...this.activePanelModalIds, id];
    }
  }

  getRelLoadingStatus(clauseId: string, docId: string): { rightContentLoading: boolean; leftContentLoading: boolean } {
    const loadingItem = this.listLoading.find(
      (ref) => ref.clauseId === clauseId && ref.docId === docId
    );
    return loadingItem
      ? {
        rightContentLoading: loadingItem.rightContentLoading,
        leftContentLoading: loadingItem.leftContentLoading,
      }
      : { rightContentLoading: false, leftContentLoading: false };
  }

  checkRelSameDoc(docId: string): boolean {
    if (!docId) return false;
    const originalDocId = this.esId || this.fileId;
    return docId === originalDocId;
  }
}
